2025-09-06 11:44:05.907 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.908 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.908 | INFO     | __main__:on_message:243 - PRI * HTTP/2.0....SM....
2025-09-06 11:44:05.909 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:05.913 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.913 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.914 | INFO     | __main__:on_message:243 - ...............
2025-09-06 11:44:05.914 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:05.914 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.915 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.915 | INFO     | __main__:on_message:243 - .............
2025-09-06 11:44:05.915 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:05.943 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.943 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.944 | INFO     | __main__:on_message:243 - ..@.........'c.F..c..[;..?..A.X=..&..u....,....(..f.+.<......Fc......l.....<4..{..".....c......IF....#.....!...LZO../...^..H..t ......~$..@..Z..m.&.K.j.d........o.....2.A..a.._p.ZA..f^.T..O.@....jm4..f..p.l7.i....h ..0.p/....`.....%.....8...Ze..&Z.0<.7....._q`...\`..hL..uQ.d!P..~..Q.VaTB/......l....w.....#%<m..#.:......u...tz...........l....t.6l.>8...F/&.$..D.6.^Y=.l...?.M..pm....^TI..pQg....K6...Q.....io..mU.?A...yra.<.q.h..-.9x.S.c..t.j...].i.G...o}Q{.............h.G.S........2y.L.O.85{../..N....a.{./.m8;N...{...a...<....G=...=pt....]....'...o.^.........|i...R~........7.Y...D.....v..........u.u..V]......n..]i.3'.E..kn....wY..7.l......wf.3...I...:<...vw.h.;...h^.&Yu...}8A.S.n..o]./\mE.k>N^O..y...a.F..fW.n....~...7....8W........6O........#....?..&..L.g<.8m.......H......O..'.......<e.!..z).W,...\....N\j.8.il...:.7^rm..=..].n.V._.....O....{oz...b.4R...kY}..Oq.<..zof.Tk..<2l.........w/.>...O.....'D.~d...4........f.........(......A.D.mF..n....i.?.....n.SW+..6^.s...._>...+.p7_#6...~rpo9.)9..k......(1..u......'.c..q.....i.....;.u.....Y...w...B^&.Q...<...%..../....-..ND..A.....d;.Ay[lY.......nX..~...E.].V....+9K....ZT]..Ev.K...uj .......Q..Kk..=R..7..{.....&L.h..;q..wo.......Hs.....z6x[.6~7....D.0.q.../.N.?.je..?....o......E.7:B'....y...e.....X.3E.>}.....7.fpi.zh....g..G....yn......!)....k.:...=8...se..<.{.n.oj..R<......>..:..No.m.~../...]........z..*2........7......k5.......Y.UY......8...0q.>......K'.....5...xc.........w.8.>.k..0K.!.........'>.kG.Z?/k.>.....).d..gx....$m...'=..w.....G..Y.n.U...Jy.....Typ.....w.....Y(.?~83c.........!.9j..?4..G_....7.....7.h<6.......f...=.~....p..xpE..gNxj....x...[E5a6.3..y...y...-...8....g.xk....''.].v.......fw",...........F/.,...g...a..f.E..v6....M...T..*y...>.?i[.MDwN...^.=.0.N....b...o..Lz.`..L^..Vl..J.^G..=..S..`....>.....&.x~.g)4Zf...Q..v^......?s....k.yi.lTBTW...B....}VUD%Y.Q.F.U....D*.1..sT.D%Y.Q.G.pt..Z.4.z..|%..y..7Z+..Cy.FZy......Z[7..)*..6...O\.....lTBTW......0.!HG^e..TBU..t&..!.w48!.......s..U.$.ar.|...}...0...|:.vw.3xoJc..[(0\.K.n..09...,.#...1....x.=..'.|v`...+\..N.2T.k...X.....i...~?.13..?.>....;0V.}.j6.O.'.<2.=.............>.Q,.[...M?/Gi.&.....W.]....}..H.-...z.{....s........k)..uf..Z<d..[...z .5F...%......M..............O\.G.#.{..-:..,.#......&.......^.W......Ly...'*Z..<.i...ha.....~...-...?.d.l....b..;.fM.Au.x.......b..^....9.........}._..&...i..N807Vi?......M.[.p.........<.v.O.v....kz.u..b..w<..]..5.|2..%...........j.....n...|...$q...d..=`w...z.`..2...!.tSoy<..&......<:3.{..G.[|X{3z........i...~Y........9=Yl.?......`.s..%..../R~...}..E.C....p.F;..~.|7...L.=....w.#..P..nS.m...^......}....3..m.q..._..r.Ci..._.&...y|y.......d.f.....f...w...5un..n....a............+...QKMg....f.....da..pc..#..q....F......7..r..j}L..oR}/lm.]...#.....sl....g.+....p..W;....-..._=.<a...P..?o~...../~Z.P.#5{d.*'..wI.....;.~k...n...O....0C68d.....o..#...5 .Ih.Q....-..#.....u.^.x<..y..........).L5.8I...G%.....g...l<.9...P.fo}.....t.{.S;...6^...8......n..i..._...].m...7....}....N....k..#w......Y......}..io>......z.....>m.=MM.....wf......f......2..4....q.X...x.Zq..........{.C..`x..zZO)..`.c?.=N..s.&....|.h..a...q^...p..,..`.t.._......B..B...oB...n....j.P....z.?Y...mp...Es.p.
2025-09-06 11:44:05.944 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:05.975 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.975 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.975 | INFO     | __main__:on_message:243 - ..............d.........................
2025-09-06 11:44:05.975 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:05.983 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:05.984 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:05.984 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:05.984 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.510 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.510 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.511 | INFO     | __main__:on_message:243 - POST /OneCollector/1.0/ HTTP/1.1..APIKey: be6e4c19699f4fdf9f8c0ec9f9b398ef-25d58bb9-9926-4df7-8e36-bc7afb4b2c34-7563..Client-Id: NO_AUTH..Content-Encoding: deflate..Content-Type: application/bond-compact-binary..Expect: 100-continue..SDK-Version: EVT-Android-C++-No-*********..Upload-Time: 1757130246985..Content-Length: 782..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: mobile.events.data.microsoft.com..Connection: Keep-Alive..Accept-Encoding: gzip....m..n.F..W.`K..6..$h..9....$M.F..d.!.............e.K...7.../.c...Z.d....[.=........o~3.vv...l..d.9..H..T.._.....~...[.O.^.>x..~..^*.0.8...a.....m....Y-...d*.g..h.....B.....f{|...2.<.3....F..J.{<EMAIL>.q.O......Z....=.2;.\.R...,..b.&uC...GvmJ.....1~.......k.P...../...h....L%..V....7s....;;x..k....6..w.J.K..l.b.&C.PA0.....C7...g....'.....&@]....0tiB=O.d../....`..T?.Z....^.Fe..V.w.G.QQDy..S....U!..Lq....q..*.Kx..}<9..4...8.%A........".z.sU.f....cE.s...J.`E.......O....v.dn.=x.u.....+;.x>.O.......=......\mS$d;..K..Ud....Pr-...O.."1..,?.10j ..M.....:4...5mb.LU.n..L'..h.._GU...l.....6gG..Q9.*+.....f..s.A..>B.W.3.....9..d........9_..;.s. ....]..!.........HO...:........p..........z./|.....4_O..#.9K....)-!7+'.S(m4....J.#..*..e..G9._j.>.u.M..Q..u.....m5....,U$.'M.W....S......
2025-09-06 11:44:06.512 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.521 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.521 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.521 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:06.522 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.522 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.522 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.523 | INFO     | __main__:on_message:243 - .........._..u.b.&=LtA..P....0Z.V{Z....{...-i[.D<..ow...X7..?j..f......P..... ......................C...cL.1...Kl/...5..=.w...P...w...%..3{.8p6.....hL..`x.o......./..aQ....P.SQ...'..7.j..S...jV4...jN..z..w....X".......o>.P..P.!.........i.Z.....f...EK....X.<.}.:SY...@...T!b...z...........t#.M..@...T!b...z.......r.......bu*w...[K...7.RMU.I.Vb.......KW..........^V%.-IXdI..>.'.(X...T]........+o....j.&J...L]#..T.i>.]f....b..A........9.....P[.jgI....P[.bu*S... .-C..H..jgI.....m1.a.O.6.s.ocI.W....6.s.ocI.W.....o?R..~..........BD....$.W..[.F.l.......n*.c..K.W...H4..r!k.)l-.C..XYKal.H.9...*.c..O..3......=F.....f[i{ZT%l....:Fe.....l.........m......_,v..O.-.3.Y'.Z..T^E.ql.b.8.Z....l7..Q.................B.;.R~..I..}h.%...h9.._W..Z.S:.J4.aQ....8...M......X4.O.....F..m-'.Y>./s..J4.aQ...w-.o5&.o.......Lz.B.5E...`.Z..2GB./.@...T!b...z.......3.D.......w....V..X.o...~MZw}_.....T..^.T..O@...T!b...z./....9G!lG....bu*w...[K...7.RMU.I.Vb.......KW..........^V%.-IXdI..>.'.(X...T]........+o....j.&J...L]#..T.i>.]f....b..A........9.....P[.jgI....P[.bu*S... .-C..H..jgI.....m1.a.O.6.s.ocI.W....6.s.ocI.W.....o?R..~..........BD....$.W..[.F.l.......n*.c..K.W...H4..r!k.)l-.C..XYKal.H.9...*.c..O..3......=F.....f[i{ZT%l....:Fe.....l.........m......_,v..O.-.3.Y'.Z..T^E.ql.b.8.Z....l7..Q.................B.;.R~..I..}h.%...h9.._W..Z.S:.J4.aQ....8...M......X4.O.....F..m-'.Y>./s..J4.aQ...w-.o5&.o.......Lz.B.5E...`.Z..2GB./.@..I.....$..K'.&~..-....:._@..I....>./..........O@..:..{:.A.-.. ......<EMAIL>....e.,..r0..2._.@.k.....3q.....e._._..Q.a..(..<EMAIL>..}.0...~.+3{.8p6.....hL..`x.o.......o......_z-4..4.U...\.i..a..s.1.@.=E.X.[;...i-D..37@...(,..k...R.j.?.<EMAIL>.q...@....8.O<.H......]......=.c(......i....}.<EMAIL>...b.a.G....?1.b.7..O.._.-!..E-."Lz...\....(.E..)l..c.......@.............a......T.=.V-....?_.#.../.q.4......+9.J?.......e....t..\a..,..Z,-g.(^....f,-g.?...)E_......@....I....N......7..@...Z..'...R-{..!Z..?...E.c[.+]2j*.....k.....m.......})..Z.5.B..h.n%..=Kg2.....k......../...E.c[.+h<...M.".......4.{).SqH..kp.mt.2..V.[.c.@...(,..k./..YT...2...."...}...e...a.v.-8.*m,.]....Y.......M..K......}`9...i..s.....4.(...Z.....f.M\..b.....................
2025-09-06 11:44:06.524 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.534 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.534 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.534 | INFO     | __main__:on_message:243 - ..........V*J-...+N-V...V*OM,.H-...sR.J@..:J..EE.y%JV.JI.E.JV....z.:J...JVJO.,......-..LW..:&%.aJ.$V.d..*Y).(.(.....(Y....JKM.)V.21.q.2..L.A....<%+#.........NC...%....."O......RiQ...QRR`......[..[............Y.....Z._....W...[...W.YR........d........T^.d....T.].d.......T..[.del.r....Z..deh..TZ...s{i....HIi.D....k.t..2.A...)..Kq.,R.225.Q....R.......d(Y...T....B..0.E..%.)JVJF.F......f!..V.FV&....V..q..1...JV.`...3.S.R.2K@.|.r...S^lY.........v*.(%......(Y...(..e.&.U......$...R...........=....<EMAIL>.e&g8'..RXN~i.s~.(.......(....&'.<_...X.RX..._Z.S.N...h..*.)..H....,..C...LA..Tr+(JM.,P.2.9.O.....i...P..+)...Z*<EMAIL>..B#C.T.=...`qi.(M(Y)=...t.JH1.......O{v=]....).R.qC.,...=...AN.&s.?....DyQbf.cn~)(..........%:&'.B.....I.Fz...:J).....NQ....l66 .`H..S"..{..LL...H....,.t. .....2.(...Uh...A.-X*.'ep*..r..b..!....z#...XG....Z^.[..y..(.......<EMAIL>.^...J.;V.[...J ..:Jy....z..1.5.R.`J."......D.9<b.,.b&.Ku..C..'..FhQ..<.
2025-09-06 11:44:06.535 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.537 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.538 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.539 | INFO     | __main__:on_message:243 - .........+.P..;..%>.................$(&.......RL .59..K........3.Hah.....v..Q2.R|.+.._...p.....B....U..R.(.'_...\....3p..*.Aj . .H..X..|...^...j0a....[b...|_?x.....w g...&`..].`.@.&.z.h.2...Q..(%..&.%&.JPb"..4.(....Cj...Z.[X.!.-..y..%h........9.......,Cs+#K+CC.e....[..Z.[....I,...PIG.dx@F"...gw<m..lN...s~.Hmx.{.Rm-.../.5R2..A..).3K.K2A..hP...8....5O......p.....[.3...,3..8.......Ut,.....!q.V...hA.A.WNbIjHe.(....%f...Cf^iIjqH~HQb^qf.........UW/.7?.0Q.H....g..=mk}.i..9./g.>...T..(.g.....[....z_...I.E...F.O;.^N..2~....J:J..EE.U.y.......R...N.%...y.P.....Rj^bRN*._...y.9JVi.9..:J......."..2sS.K.A}@....L...A$....CC.....(...........Tpx..&.%U...$...JP..c.......Dku....JR.JR.K..].LP..."...8.$5.T....@.....U.F&p}..*..&...9C..!....RfqpA~I..U....m`.J2Kr@I....9w...;.7.:.......O.L..?nhz.w.....v.|.k.....L.!.?......_.W\...g..C*..}X.P.F...D....%...$&..RljQbIiQ.Rm,.._P._....WV+.%.G...=KH.\.J...(//...........^....N.)JV....qf..i...].%J.:J..U..p..t..T..X...@Z.E....q.j..%+SSK...Ks..J%+#s3#sCcK..*p.)O6P......A.Z...SC.##..c.#b...X..........a...9B.....a=&....=....8..B..8....)..@.<EMAIL>.....n.......M....,M,..-kA2....Z......y.U...J:J...Z.Ge.fe&..+.g.......)...R.Q..@.;.).(.F:...R....3...3...3.3..... ... .h...<n.)....y%E..[jA..4/.4.W.T\Y\...d...ZR.....E.....o.....*.Rs.`..J...gP/. .<r..u....}H..*/S!.[.....7..qR....D1.D..N1(.........e.r~.RbiI.KjIj2xd.Z)=5..#.2KJA-'P.............XB...*..@.s..cCsc.#.C#PS..*.jH)......V.!......DX.Edf.B...j...!..&RZjQ*.l7.....T.A..9..V8.R.T.(.f&......($..(d..+.....d....)8..(...+.........).dd.+8.x*$'....($.*$&'.......(.HU...`~b^%HwiNI.Bnb..TOiqj..BQjAQ~Jirj.B~.B.....,)IMQ..S...M..K-R(.,../-QH...0.."..<......bP4(...."y.9.. ......j...m._...
2025-09-06 11:44:06.539 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.539 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.540 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.540 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:06.540 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:06.798 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:06.799 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:06.800 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Content-Length: 9..Content-Type: application/json..Server: Microsoft-HTTPAPI/2.0..Strict-Transport-Security: max-age=31536000..time-delta-millis: 345..Access-Control-Allow-Headers: time-delta-millis..Access-Control-Allow-Methods: POST..Access-Control-Allow-Credentials: true..Access-Control-Allow-Origin: *..Access-Control-Expose-Headers: time-delta-millis..Date: Sat, 06 Sep 2025 03:44:07 GMT....{"acc":1}
2025-09-06 11:44:06.801 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.256 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.257 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.258 | INFO     | __main__:on_message:243 - ......................Q92....H.=...N;~..c.. ..F...-.).&.....^.D..H.>.('..............+./.,.0............./.5.............edge.microsoft.com............................#.........h2.http/1.1..................................3.&.$... ..e.fu..G.."....y.l.....J{..u`LI.-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:44:07.259 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.523 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.523 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40880
2025-09-06 11:44:07.525 | INFO     | __main__:on_message:243 - ....M...f..h....r...=M2P&...`..7..L._(.8.T. s...ds.=.....v......_....D...>u..0........#.........h2................$..!..k0..g0..O.......3.0).80.........0).0...*.H........0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080...250819121432Z..260215121432Z0i1.0...U....US1.0...U....WA1.0...U....Redmond1.0...U....Microsoft Corporation1.0...U....edge.microsoft.com0.."0...*.H.............0..........s..\..[p[.A...........\.Jb.....JJ...I.D..k.p... 4...l.Qkl(...#......\w.Z}..\....#%/;<,.+...I..&.w...i..W.T.....I.....A..z..i..[.j.X.s..'.2..ST......w.....<.y1....~...Y....m.Q.C......H..Z....R..,..X..S)....y0|;8.d.......FJV.".._O...QA..lsN.(F..."u..:x...A.........0...0..|..+.....y......l...h.f.v...d.UX...C.h7.Bw..:....6nF.?.........I.......G0E. #^...D.;sY..7Y4n....4..K.e.....m.!..6p.....%..o.=.1......M0k)...o.e.u.d..l............O(...5'......}.......I.......F0D. 1..M9.M.....e.2a...hi\b#.....)o.. 6.D........F..-?6[x..h....K'.....u.Vl..v....B.u..#$..i.....I..}..-......I.......F0D. ^;bj...Q.Hf.z^.Al."_.Vt?.F..._... ...w...R0...L.].P..x\o..9..G....0'..+.....7....0.0...+.......0...+.......0<..+.....7.../0-.%+.....7.........F...........]...0......d..-0....+..........0..0s..+.....0..ghttp://www.microsoft.com/pkiops/certs/Microsoft%20Azure%20RSA%20TLS%20Issuing%20CA%2008%20-%20xsign.crt0-..+.....0..!http://oneocsp.microsoft.com/ocsp0...U..........d.[4......W...n.0...U...........0...U....0...edge.microsoft.com0...U.......0.0j..U...c0a0_.].[.Yhttp://www.microsoft.com/pkiops/crl/Microsoft%20Azure%20RSA%20TLS%20Issuing%20CA%2008.crl0f..U. ._0]0Q..+.....7L.}..0A0?..+........3http://www.microsoft.com/pkiops/Docs/Repository.htm0...g.....0...U.#..0....~/...J.p[.......a..0...U.%..0...+.........+.......0...*.H.............3.:[Z.....~..k..z.._2.f.g.....ec..f.....[........3.....L.Sh..NL..YW..X....9E..;M..F_..x...,(.F.}..4.j....O7.....".......V...=.R...>.]..p....i..@f(..3..\....^...<EMAIL>..(..G.n.X.....f..........6e..#.=.h.I....]..+'`HW.F$%...%J....<3..E..1.. t..Q]jA.v^......gM..H..'n.....HWQ$O~..[..~.....#...r>.4.....S...+.............b.d.......*........._<..........\.9...b.3....q.....6......."...c...........{=U.........`M.P,.8.^..U.zE.2.kV+V#..ER....l........(..T,....&U.2T...rf...9.%[,j1...D].@.T.k.$..3_...,.<...J..kM.C....:...0...0............~T~......Wim{.0...*.H........0a1.0...U....US1.0...U....DigiCert Inc1.0...U....www.digicert.com1 0...U....DigiCert Global Root G20...230608000000Z..260825235959Z0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080.."0...*.H.............0...............eV..fmK.._..){........@.....c.N..*.DG.....x........C_.O.....^a+.+#.E...D.V'`.[.....M...8.<...7.y.9... .^.8.T8.2c .z....i......A?.w(RE...H.h...-.g.\.f..HX.!Y.d.,..=...2..{u...@.q..4..E.=.'g.........s*7....CR..1...``..,VbQ.......k..79b.GKaE..S.(.J.....y..Q.......,.V^.N.......%JQE%...G.......nQ...`....$..l;.1...."..>.......j).+du.&0..,..ieg..].R.......6...B..C^.{...E.=.c|O.....?....i..TJ....@.......?..<7v..(.k.7.:z........A....^....]...XHv...A......n.01.....s.=8.6...M......)..........r.........../.%..F.........b0..^0...U.......0.......0...U.......~/...J.p[.......a..0...U.#..0...N"T ....n..........90...U...........0...U.%..0...+.........+.......0v..+........j0h0$..+.....0...http://ocsp.digicert.com0@..+.....0..4http://cacerts.digicert.com/DigiCertGlobalRootG2.crt0B..U...;0907.5.3.1http://crl3.digicert.com/DigiCertGlobalRootG2.crl0...U. ..0.0...g.....0...g.....0...*.H................p.x....b.k.y.../.S.Sly.yI.g>.n<........2~.+..}..F#3j~......wM.......U.....[.......`.....v6..h..I...0.............{C/.......~Y=...!..T.?%...L:.6.........+....Z|........VU.|..G..>....(.. k.!.~....gfn....\]....<EMAIL>?......c..3.V..n-..(.....2.....4F......F...B0..>......70..3..+.....0.....$0.. 0........!.....6ye.nK.xi..&..20250901223749Z0..0..0L0...+..........c...b.u..+ti.R..*....~/...J.p[.......a....3.0).80.........0).....20250901181308Z....20250909183308Z. 0.0...+.....7......250905182308Z0...*.H.............z.....^.....S.3m...q...(..[...........8ns.=._6$.NN.^...p...#=.....M_..Y.Q...s!R\.i.5..=9.........d.0VK....b..YI.\q.U.[.x.4.W.}7....6..W+.b.N.....F........b.5...G?..2C.......O.....`.....p..........[;.....E...{.M.7...........A3.......l.S.f..80rW.S..m.h...`.....@0..<0..80.. .......3.>.@A.ge.M.s...>.@0...*.H........0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080...250901155027Z..251001155027Z0k1.0...U....US1.0...U....WA1.0...U....Redmond1.0...U....Microsoft Corporation1.0...U....AzureRSA08 OCSP Cert0.."0...*.H.............0..........B...V.L.R.9..z;..yd"/GH$..@.x....O3)Jn.....].@R.a...N.VI......a..........Z...).o.U/u.e.i3/}.1.sK..OX?.."....PK.9L".~%.................".v...{A.B...[.....#....b..d...~h$....U...6.N...%.._.9,cw".q.>....q.3.x...un...N...P.9...b..H...L.......9..!9%.VZ..#nPU.........0..0...U.......0.0...U.%..0...+.......0...U........!.....6ye.nK.xi..&0...U.#..0....~/...J.p[.......a..0...U...........0<..+.....7.../0-.%+.....7.........F...........]..."......d...0...+.....7....0.0...+.......0...+.....0......0...*.H.................CUeM...)t.68\.....H.&.1.P+.@...p.....[q].Hc..EP*:./sdX.4.......7...g#T.#....Zkr.B..Am..~..a....0n......'....2.....C...H..*............E.!...........ygS.d..M.L...5....M74JZ[>...g..F...(....L..l..\3.;a...J...WY.a.'J..'..n...pA...]W.YB..,.8>#....X...@..Q\......G2..~...'...t..;*[d...X.I:.V.@i...B.....&.c]..#.AX...<..F|^.,.(....h..|..T..1E.cRH[..Z..B..Y.,]..|..{kV.c.I.......X...y#..D.#.....:..no...c...*Tp...z...;..9Xh.S...T.|0Fz.7/.?B..+..boP.S_....89......y)[..UO.K.....#..jD.^.<..O..b.>..Nf....r.........JW....i...a..._...ZO-.p......h...i..Gn..[.!....[.^.}..J......E.%mcW.....3.*........;..g.R..x.p..W..g..j%...L....-bN....GW..:..f..w..>.n.....`.$>...W?.....a...\. V*nT4....."..}...,..-..a.#..~`he.....lP.o.(T...KpisP.)..Z.l..{.`......i...=f...%E.>...2B$W.W|....h{.?...<4n..&6.......@..J.......~.c..?Aj.}.=.J..O$.U.I[.0;..i.wy....3P^a..PW00..3/.....^U.:../..!...;Q.+~.3S......
2025-09-06 11:44:07.525 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.545 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.546 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.546 | INFO     | __main__:on_message:243 - ....f...ba.. .j....f..r......D.N.F.z.....i.....4=D.k4..C..3. ..M.w..C..g.5......w.....6>.p U......a..v5s9.O..........(..............d..c.nS\.f.....PH+.nA...G.
2025-09-06 11:44:07.546 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.695 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.695 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40880
2025-09-06 11:44:07.696 | INFO     | __main__:on_message:243 - ......................x=..O..9.6.....4~....pQ.H...RA..1...z...W.!u..]i............*E.)../..?Vmjo.<V=..e..:@R....([...3.2......8...7M...oe.l..Z.frj...v..:.3.p.M..i!&q.....m...u.O.....g~E}...M...].. .[<.=..~.+,...}lWU...e....v[-$....g..T.........h..:E..j...........k....k{z..J./7.O..>...a...............(........#.....<......'_;|.XSE....,... f.....@.........%....&H.R|,.f.r..S...v...kP....z...E.b=.....A......L..9
2025-09-06 11:44:07.696 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.697 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.697 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.697 | INFO     | __main__:on_message:243 - PRI * HTTP/2.0....SM....
2025-09-06 11:44:07.698 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.708 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.708 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.709 | INFO     | __main__:on_message:243 - ....0..........y.....Y....I...:U:..>..a.\<..ai|~..1..
2025-09-06 11:44:07.709 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at KD6.h(340511105:135)
	at okhttp3.internal.connection.a.e(340511105:223)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.709 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.710 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.710 | INFO     | __main__:on_message:243 - ...............
2025-09-06 11:44:07.710 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.714 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.714 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.715 | INFO     | __main__:on_message:243 - ....'........._..f.$$%.[!V.Y..#.].<...+G.t..
2025-09-06 11:44:07.715 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at KD6.h(340511105:209)
	at okhttp3.internal.connection.a.e(340511105:223)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.715 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.715 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.715 | INFO     | __main__:on_message:243 - .............
2025-09-06 11:44:07.715 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.727 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.728 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.728 | INFO     | __main__:on_message:243 - ....%..........R.......3...I..C....[./0...
2025-09-06 11:44:07.728 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at OE3.j(340511105:37)
	at KD6.h(340511105:228)
	at okhttp3.internal.connection.a.e(340511105:223)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.729 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.729 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.729 | INFO     | __main__:on_message:243 - .._.........a#.k.mi2h,....&=L..1I.5.`u..)..J..z.d...#..S...D..=}`{A.,..zLK.....z..P....z.?Y...mp...Es.p.
2025-09-06 11:44:07.729 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.730 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.730 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.730 | INFO     | __main__:on_message:243 - ..............d.........................
2025-09-06 11:44:07.730 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.754 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.754 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.754 | INFO     | __main__:on_message:243 - .....................[./V..>.X6......O.O;O.[%.sR.d.....f..z@.g..,j0.....<V,i......1..ci {.....2.......`.........1.[+}.......*...[2...
2025-09-06 11:44:07.754 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at OE3.flush(340511105:8)
	at okhttp3.internal.http2.c.a(340511105:261)
	at Zk0.intercept(340511105:33)
	at YD6.b(340511105:121)
	at S41.intercept(340511105:137)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.758 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.758 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.758 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:07.758 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:07.780 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.780 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40880 --> *************:443
2025-09-06 11:44:07.781 | INFO     | __main__:on_message:243 - ....!..........hB....D.s...d...B......
2025-09-06 11:44:07.781 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at OE3.a(340511105:110)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable$settings$1.invoke(340511105:167)
	at ps8.a(340511105:25)
	at us8.a(340511105:16)
	at ss8.run(340511105:18)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.902 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:07.902 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40880
2025-09-06 11:44:07.903 | INFO     | __main__:on_message:243 - ....!........l.j......L.8.0.C..;.'....
2025-09-06 11:44:07.903 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at o04.H1(340511105:36)
	at DD.H1(340511105:8)
	at ED6.H0(340511105:23)
	at ED6.v1(340511105:1)
	at okhttp3.internal.http2.d.a(340511105:10)
	at IE3.invoke(340511105:16)
	at ps8.a(340511105:25)
	at us8.a(340511105:16)
	at ss8.run(340511105:18)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:07.906 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:07.907 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:07.907 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:07.907 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:08.076 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:08.076 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40880
2025-09-06 11:44:08.077 | INFO     | __main__:on_message:243 - .............rU....g.9x...1>sxI.T$..F^>.6Y..........Y..e.N...DE.d.O...(.K?._.?.S.......L..6T....GQz..oB7,zy.......R.ew.z~......#U.Z%....k...j}.os....2:............^...h...B]....D..X..sz^....O......$...U2..Y...V....c.:_....\..^.9........S)....(...U....t.../m...b..9....nI6]F..'.Wl.8^."%....(..`.S.|.....\&<X........[.G..E)o...hU4.ff....$...=@.1..8.A.....8. ~z\...H....z`.....b..~.7!O........[.<EMAIL>%.......K..U..h.}.&^..O.k0.f..gU}.c/.R.$.r.@_Y.;.Bc......s...S%\.K...4..8..]}.e(.....x.Y...Nh.U......RA.Pd..}.....G./....T5n..,.v"jf>..(...Ox...f..._3j...$..U1>S....U...C.D0..3..B...mt.....G.........=......+..<EMAIL>...l..../{.................;....`...P...zX.J...0.Z.tA{......_...!..]O.lm.]Q..H..xsV.../.^.w]}X9i.n.<...h.....\..-Y..C...5..-.=....3..........._........!....;7...rw^e..tC!...Sng... ...%..0.......9....G~.....M....jYw....[."..fs..[p..W..7...o*...AG..`(u0.!.\...{.L..g..RjC........Qz,...d[....|8...>..@.......)7.D..7u.%.......L...6...E'.)...[.q...9+]H%...Y=M.i.{]...._.I.....cI.dO![.:).........p.v.IO...W.}........'....8...2l.O.J....Y.$hXY....+{..^.4V..l.`...o.I?.wM..9.e.w.......H..M.._4.M..............p.<>....xx...1..?#g.<..P..=..s".4.o..5x.:kz).....@.wm..Y..L.{...W>&......P.V......ja..$.>'a...C.h.........G.O..L;..{*......tE}%.+......./4H....Kp.@..'.....[..%?4..ST.mxo=..T....x.ya...G.J.Y.d...C..n.M.......W.A.^....2.>K.//ZH6J.N\.....~.~/DS....Y.D@.E.>......Yo..5+.x|..!GP.0+...P........<g..%..p.xK...'...d.+.....................t;......Nfe.!d..a.....G.....I8....:....bk#D..H6.-....}.R."..Fb,a8..> .K..FS..Gj7.....=D..*.iKY.-H0+0....:..(..?..x..V...zc..,.._.....C.c].f........W.....M.R)%X.h......9...Dg......J..nX..4.)52c.w<.....!..........c.......M..mq..."....?.
2025-09-06 11:44:08.078 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at o04.H1(340511105:36)
	at DD.H1(340511105:8)
	at ED6.H0(340511105:23)
	at ED6.v1(340511105:1)
	at okhttp3.internal.http2.d.a(340511105:10)
	at IE3.invoke(340511105:16)
	at ps8.a(340511105:25)
	at us8.a(340511105:16)
	at ss8.run(340511105:18)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:08.078 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:08.079 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:08.079 | INFO     | __main__:on_message:243 - ..........X..~V......?\../5_..u.b.&=LtA..P....0Z.V{x..~V...M. ..@....I....N......7..@...(,..k./..YT...aq...........4..u...N..T.YT.qM.4....`.D.6.U/\P@.k.....3q.........4.(...Z.....f.M\..b...........{"marketSiteResult":[{"url":"https://open.spotify.com","title":"Spotify","displayUrl":"open.spotify.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fopen.spotify.com"},{"url":"https://m.facebook.com","title":"Facebook","displayUrl":"facebook.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fm.facebook.com"},{"url":"https://www.amazon.com","title":"Amazon","displayUrl":"amazon.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fwww.amazon.com"},{"url":"https://www.hulu.com","title":"Hulu","displayUrl":"hulu.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fwww.hulu.com"},{"url":"https://twitter.com","title":"Twitter","displayUrl":"twitter.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Ftwitter.com"},{"url":"https://www.ebay.com","title":"eBay","displayUrl":"ebay.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fwww.ebay.com"},{"url":"https://www.walmart.com","title":"Walmart","displayUrl":"walmart.com","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fwww.walmart.com"},{"url":"https
2025-09-06 11:44:08.080 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:08.080 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:08.081 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:08.081 | INFO     | __main__:on_message:243 - .........://www.dailymail.co.uk","title":"Daily Mail","displayUrl":"dailymail.co.uk","iconUrl":"https://edge.microsoft.com/sai/api/get_favicon?url=https%3A%2F%2Fwww.dailymail.co.uk"}]}
2025-09-06 11:44:08.082 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:08.082 | INFO     | __main__:on_message:234 - SSL Session: 0ED1981624CD885780436831024DEB85F24B1EBD273E62183659AE66B4AA4AB0
2025-09-06 11:44:08.083 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:08.083 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:44:08.083 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:10.096 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:10.097 | INFO     | __main__:on_message:235 - [HTTP_send] *********:48534 --> 204.79.197.237:443
2025-09-06 11:44:10.097 | INFO     | __main__:on_message:243 - ..........."B.R....d...?.9}f.).(mo...K..[.. ....Z.`}.`%\d+j....T.e.M.j.s...{.........+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... .....|8......{........NW4 .%z!.o.-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:44:10.097 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:10.256 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:10.256 | INFO     | __main__:on_message:235 - [HTTP_send] *********:37096 --> 13.107.21.237:443
2025-09-06 11:44:10.256 | INFO     | __main__:on_message:243 - ..............q`z.i.q*..j..G^.v.L.C.....'./ .-;....2n9b. [.tIe_%>fIMq......_.........+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... ....2.....y..-O.7%*.v...?[.#...g.-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:44:10.256 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:12.399 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:12.400 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40900 --> *************:443
2025-09-06 11:44:12.400 | INFO     | __main__:on_message:243 - .............Ik'q7]........v..zi....MBD.... ...'.b..~...nG."T....a=.S.'......".......+.,.../.0.............../.5.............edge.microsoft.com............................#.........http/1.1..................................3.&.$... ......$.Z.~+.R..aU....h........`.-.....+.....................................................................................................................................................................................................................................................
2025-09-06 11:44:12.400 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.connect(DelegatingHttpsURLConnection.java:90)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:94)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:12.725 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:12.726 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40900
2025-09-06 11:44:12.729 | INFO     | __main__:on_message:243 - ....S...l..h.....t7.w..9...}`.@..../.O..v.a .<......U.}#..C.....[.Xx2>..\..w.0..$.....#.........http/1.1................$..!..k0..g0..O.......3.0).80.........0).0...*.H........0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080...250819121432Z..260215121432Z0i1.0...U....US1.0...U....WA1.0...U....Redmond1.0...U....Microsoft Corporation1.0...U....edge.microsoft.com0.."0...*.H.............0..........s..\..[p[.A...........\.Jb.....JJ...I.D..k.p... 4...l.Qkl(...#......\w.Z}..\....#%/;<,.+...I..&.w...i..W.T.....I.....A..z..i..[.j.X.s..'.2..ST......w.....<.y1....~...Y....m.Q.C......H..Z....R..,..X..S)....y0|;8.d.......FJV.".._O...QA..lsN.(F..."u..:x...A.........0...0..|..+.....y......l...h.f.v...d.UX...C.h7.Bw..:....6nF.?.........I.......G0E. #^...D.;sY..7Y4n....4..K.e.....m.!..6p.....%..o.=.1......M0k)...o.e.u.d..l............O(...5'......}.......I.......F0D. 1..M9.M.....e.2a...hi\b#.....)o.. 6.D........F..-?6[x..h....K'.....u.Vl..v....B.u..#$..i.....I..}..-......I.......F0D. ^;bj...Q.Hf.z^.Al."_.Vt?.F..._... ...w...R0...L.].P..x\o..9..G....0'..+.....7....0.0...+.......0...+.......0<..+.....7.../0-.%+.....7.........F...........]...0......d..-0....+..........0..0s..+.....0..ghttp://www.microsoft.com/pkiops/certs/Microsoft%20Azure%20RSA%20TLS%20Issuing%20CA%2008%20-%20xsign.crt0-..+.....0..!http://oneocsp.microsoft.com/ocsp0...U..........d.[4......W...n.0...U...........0...U....0...edge.microsoft.com0...U.......0.0j..U...c0a0_.].[.Yhttp://www.microsoft.com/pkiops/crl/Microsoft%20Azure%20RSA%20TLS%20Issuing%20CA%2008.crl0f..U. ._0]0Q..+.....7L.}..0A0?..+........3http://www.microsoft.com/pkiops/Docs/Repository.htm0...g.....0...U.#..0....~/...J.p[.......a..0...U.%..0...+.........+.......0...*.H.............3.:[Z.....~..k..z.._2.f.g.....ec..f.....[........3.....L.Sh..NL..YW..X....9E..;M..F_..x...,(.F.}..4.j....O7.....".......V...=.R...>.]..p....i..@f(..3..\....^...<EMAIL>..(..G.n.X.....f..........6e..#.=.h.I....]..+'`HW.F$%...%J....<3..E..1.. t..Q]jA.v^......gM..H..'n.....HWQ$O~..[..~.....#...r>.4.....S...+.............b.d.......*........._<..........\.9...b.3....q.....6......."...c...........{=U.........`M.P,.8.^..U.zE.2.kV+V#..ER....l........(..T,....&U.2T...rf...9.%[,j1...D].@.T.k.$..3_...,.<...J..kM.C....:...0...0............~T~......Wim{.0...*.H........0a1.0...U....US1.0...U....DigiCert Inc1.0...U....www.digicert.com1 0...U....DigiCert Global Root G20...230608000000Z..260825235959Z0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080.."0...*.H.............0...............eV..fmK.._..){........@.....c.N..*.DG.....x........C_.O.....^a+.+#.E...D.V'`.[.....M...8.<...7.y.9... .^.8.T8.2c .z....i......A?.w(RE...H.h...-.g.\.f..HX.!Y.d.,..=...2..{u...@.q..4..E.=.'g.........s*7....CR..1...``..,VbQ.......k..79b.GKaE..S.(.J.....y..Q.......,.V^.N.......%JQE%...G.......nQ...`....$..l;.1...."..>.......j).+du.&0..,..ieg..].R.......6...B..C^.{...E.=.c|O.....?....i..TJ....@.......?..<7v..(.k.7.:z........A....^....]...XHv...A......n.01.....s.=8.6...M......)..........r.........../.%..F.........b0..^0...U.......0.......0...U.......~/...J.p[.......a..0...U.#..0...N"T ....n..........90...U...........0...U.%..0...+.........+.......0v..+........j0h0$..+.....0...http://ocsp.digicert.com0@..+.....0..4http://cacerts.digicert.com/DigiCertGlobalRootG2.crt0B..U...;0907.5.3.1http://crl3.digicert.com/DigiCertGlobalRootG2.crl0...U. ..0.0...g.....0...g.....0...*.H................p.x....b.k.y.../.S.Sly.yI.g>.n<........2~.+..}..F#3j~......wM.......U.....[.......`.....v6..h..I...0.............{C/.......~Y=...!..T.?%...L:.6.........+....Z|........VU.|..G..>....(.. k.!.~....gfn....\]....<EMAIL>?......c..3.V..n-..(.....2.....4F......F...B0..>......70..3..+.....0.....$0.. 0.......Q/...W.F..<.Y.-...u..20250903031030Z0..0..0L0...+..........c...b.u..+ti.R..*....~/...J.p[.......a....3.0).80.........0).....20250903021308Z....20250911023308Z. 0.0...+.....7......250907022308Z0...*.H............./.../...XQ..!@.d..I4...VCzD.....o......7.....{..^...a.*%...wI.zG..&.v..9....:..x.Q-...s.E.H.t..:.).eQ\s]..)....];yk. ...Z...DA.3....D....'WVS..=..,Z[^............0O..Q.......,;c...Y-.......h....~.fx...hZ4.......f.}p....f.K..l0.....i|_...[.E..G.......O2.2.4...@0..<0..80.. .......3.@.-...*..fT...@.-0...*.H........0]1.0...U....US1.0...U....Microsoft Corporation1.0,..U...%Microsoft Azure RSA TLS Issuing CA 080...250902155027Z..251002155027Z0k1.0...U....US1.0...U....WA1.0...U....Redmond1.0...U....Microsoft Corporation1.0...U....AzureRSA08 OCSP Cert0.."0...*.H.............0..........p.O?{zs&Y..;.........O`y....E..h[.1..yN_.S..|...".G...L...k.s.K.......z.wN...B%..LD2.'.A..]..k.....eK,.....)._.m....k...3&.LX.>.=.m.z.x.S.Uf.N............7..kD.`...6$........k...T...T.y.x..-|....m..I.g4&....t..A.......j.0..V.- ._.!...Y..(.7...K(..W|.............0..0...U.......0.0...U.%..0...+.......0...U.......Q/...W.F..<.Y.-...u0...U.#..0....~/...J.p[.......a..0...U...........0<..+.....7.../0-.%+.....7.........F...........]..."......d...0...+.....7....0.0...+.......0...+.....0......0...*.H.............1..m.0.....m.vRM9.....R...z.C.x.......[%...`...!:h...zPE>R.......'..+s.um..~W./.]!".Z.6....Q........4.e..5......yhF.n.7...]8,....V...+...!.....-...H?.....%m..).G......H@.......[.].G\.Q.......$....y.@.}L......y..H3..+."..vl..d9.D)..5G!G.l...B..$._....I=..!.-`<1+... /:y2..U.(....-.p.e..y.{.l.OF...>_-~.EU."..)'......(.|....|..<.t}.....\."...1....................x.Dcs.Y.....C.d6...\..h#=s....(...t..;....L......=..-uL.<M..M....@.........5.Q..%.g.....(...4...!.........2..N/..........OB.....y....Z..H..$.Q....S.U.....i...a.q......PzDf.$l48..e.-......P8..'.....W.PK......qa....e...SPZ.....!K$j.hV......'....hE}../.kQ........E.0.28]*.k..=.Q.........s.BU<N{?...s.t..VI.....{.[.[Mr.azC.Q...[.6...U&....t.n.wT.<73.O.....$...~.I%..I..n.....H.-...=....i?.g.....Ir.?$vdi.:...OVsn.R..?.7.OYT...!..}."Q.;.r...q.y.......@}c.Y.k......_......8...Ck...~.r..\..F.....<....Nd....m&T.d....=.{.=......
2025-09-06 11:44:12.730 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.connect(DelegatingHttpsURLConnection.java:90)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:94)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:12.757 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:12.757 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40900 --> *************:443
2025-09-06 11:44:12.758 | INFO     | __main__:on_message:243 - ....f...ba..6... ...x...i.'...b..xd..t.*......9..........YG...x.|N....,?..A.hXn...D.-..c?.=.-..P_........j[..........(.............u........~=.7...[.*..E_3MC.
2025-09-06 11:44:12.758 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.connect(DelegatingHttpsURLConnection.java:90)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:94)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:12.830 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:12.830 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40900
2025-09-06 11:44:12.831 | INFO     | __main__:on_message:243 - ......................x=..O..9.6...!.v................{.1....Z......U...........#...$fWh..h...G...2...VSc..?K....3R].....<..M..|T...V../.Y/...>.."!.z.?...2j..!..........yk........L.....?...c(../....H}.F..[[..E.el!.....>......:.a......5s..x.P.G...QCX;..9..g3..2..V..JL.e:...T.F..Mi.........42..........(..........Oq.)...HpY5LN|.|.....w.L^y...x
2025-09-06 11:44:12.832 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.connect(DelegatingHttpsURLConnection.java:90)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.connect(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:94)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:12.832 | INFO     | __main__:on_message:234 - SSL Session: 7F284BC1A1509A80AEC9BA2E00EEB97B69672B71880CAAA6C3E09CF235298A93
2025-09-06 11:44:12.833 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:12.833 | INFO     | __main__:on_message:243 - GET /favicon/v1?usesai=true&origin=mobile_default&size=125&url=https%3A%2F%2Fwww.ebay.com%2F%3Fmkcid%3D1%26mkrid%3D711-53200-19255-0%26siteid%3D0%26campid%3D5338777411%26toolid%3D20008%26mkevt%3D1%26customid%3Ddefault-edge-mobile-android HTTP/1.1..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: edge.microsoft.com..Connection: Keep-Alive..Accept-Encoding: gzip....
2025-09-06 11:44:12.833 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:12.854 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:12.855 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40900 --> *************:443
2025-09-06 11:44:12.855 | INFO     | __main__:on_message:243 - ....................om..V[... .wO....pw.Vx.......v{..t*t...._..........f`Nw..X=...p.....0q!;^....`?...L.....y.B....*?kb.a....&>.~.`..........gNd.7..Dc@.=<EMAIL>..{.=..E..].6.'#{jJ.P.(.....b*.f..i.(2,..P.....v...e......j..6.Z:5,._.UM......<..BXB........c..>.#0........F.........o<..Z....g.G.i.g........kM{......>6..s_.1..F..@u..X..................T..H...f....-.E.. ...O].X....x..N..4ep5j..Iu.........-.KwHI./..b|.`B3"..M.^[.&.
2025-09-06 11:44:12.855 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getInputStream(HttpURLConnectionImpl.java:248)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getInputStream(DelegatingHttpsURLConnection.java:211)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getInputStream(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:99)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:13.172 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.173 | INFO     | __main__:on_message:235 - [HTTP_recv] *************:443 --> *********:40900
2025-09-06 11:44:13.173 | INFO     | __main__:on_message:243 - ....r.........#.....@.BI.(.*.3...o(.....m3I.gF.p....Y..dc..g.I...!.I.8a..QBoy.........x...!.Po^d6..R..\ .Rx......"M5.[..;.`..^...N..J.|^..t...[........Cw......i........(.....<EMAIL>..'u2i....$..t...$W$.....~..M...V...b(.d.....A..u;.]..OX*.$/....!......X..K.t.Z...K..m...8.u...s.../1...Q....'e[...o...]J...<)..\......C...*..@R.S^.[.L.=......u.v`.l..S-.~......3.{Q...e.hj}..yg.=`..!P.........9.B]c...]/.3....!u.7.ZkR..S.B.r......jmg.).......rEi-d....Ax^'=..VS<......_.-..d..P..R.%....-#wX|....qG...C.\f..!..M[drA.......B..0D.&.A....r.L7.i.J.H%jA8.....)q......D..R....".&I...r"8S...Z.>.c.s.q.0|.x.rk..P.....03X........."....k.{.5G. .X/.w.....&....P...(Y..9..0...z.hu...r...{X.\`....R9..2..H.FH2..*...F\....o...cm....?f..`.V..... ..<.>..1..De.[........h.15]..u"o..K.....:....P.\../.j;.9S.xj.y.V~1n...^.vo..Y!.....=.T0.L....#.........T.S......8.."..w.Lx..&....q....3..h.>p..]8x.T.'n`.E...0n.t.b..............r....8...9H..3....^.W.t~-L..dU...e<.2Lx#.O.|;.....l.N.:[$wE....pR8. ..?....1q^.j....X......!e%D....C....a.......7....>.Q..h.h....5.#v...'*...W...ZN...y..=s...`.d_o.c....b.p.C}....$H.S.O.8^5\!..:..-.....nLr.._..(W(..]).j...q,..... s%..X..B8k....[...C.*.W0...h~ `...I...V+...[..DJ.]...[..<EMAIL>[c......#.f.q..w.8m.sC...f.4.i&.....)......v.i3...p.,...!8..f......E...Y....7..<....=............hW).........p...a.W.p.....=......4\>:...!.m.!.m]...Ld.....&......8.Na...........=......z.=.Z..Uy|.5N....G......^.......<EMAIL>.......9.G.!..K........Uh[ot...J.....{.n..;.c.8+f..S.I..q.a..d>. ..%..o..{.<EMAIL>..;G.@.)..O.(]E..vV.N.2SI....=.|..LC..h....A<..P......Bg..x?{.....-.^.R...\..*'m.TuHDk..\..{SS./?..$.k......x...._....UrQ|..n..3..[>.TE.e..6...`.k.v..\6.L.R7E....H..V.....1@.......q....W...E?d^Kl8,d.7....g=...\......*.d....c....P5....C+.[.b..........0......+..T.t..C.P.o.9/.X......o..uu7Oz..|)....C.@..M..>.H..>/=Fw........0../......=....^v$e0..w..%.......Mf..o|cC......O1:.b-...L<..v%....D...H+{*x......!qp0t...~.Rm{?.N...].Q.$...rR.CK.V........JI8..P......g........uI........S.*...%=..p).c#.%....C....E..2.....4,.Q..dM...WV..SOg.....5\..x..;..$.L.N....a.....".;..7i..K\.H...5\%i.%?...........!.2....V{...<EMAIL>....N ...X."V.......u.Rq08.2TqdY....`.........-2...........'..S.6.~.Z27.x..C..JP.. .E...;....:.q..^..V..@gY...F9...x{.*.M<;....n>.L.....Y.&0......<......5O..Y..{|....{.,..........L.....AU....k/.....p..$H.M{........_3^........W.^..9.+....E..N..0=..B...2...$......a....>..*#J..C.y...G..[.O..a...k@.,.....|>...7d..rj.UG...@...j....J.)....|.9.57wP..y]UpF..5......g..=..q.y\vZ.......kd..~%.ZY..y..Br.N....G..9..xb<<..w........4.V...k.....GT......'-..........C~....t....h:..d.Q.5.....b..^..........+.k}...F..2Z.q.v.3....1.....
2025-09-06 11:44:13.173 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getInputStream(HttpURLConnectionImpl.java:248)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getInputStream(DelegatingHttpsURLConnection.java:211)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getInputStream(HttpsURLConnectionImpl.java:30)
	at AG3.c(340511105:99)
	at AG3.d(340511105:20)
	at gc5.d(340511105:25)
	at NC7.a(340511105:129)
	at com.bumptech.glide.load.engine.a.k(340511105:23)
	at com.bumptech.glide.load.engine.a.l(340511105:41)
	at com.bumptech.glide.load.engine.a.run(340511105:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at nr3.run(340511105:29)
	at java.lang.Thread.run(Thread.java:1012)
	at lr3.run(340511105:6)

2025-09-06 11:44:13.177 | INFO     | __main__:on_message:234 - SSL Session: 7F284BC1A1509A80AEC9BA2E00EEB97B69672B71880CAAA6C3E09CF235298A93
2025-09-06 11:44:13.177 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:13.178 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Cache-Control: max-age=0, no-cache, no-store, must-revalidate..Content-Length: 2056..Content-Type: image/png..Content-Location: https://edge.microsoft.com/favicon/v1?url=https%3A%2F%2Fwww.ebay.com%2F%3Fmkcid%3D1%26mkrid%3D711-53200-19255-0%26siteid%3D0%26campid%3D5338777411%26toolid%3D20008%26mkevt%3D1%26customid%3Ddefault-edge-mobile-android..UpdateTime: 241126..Key: ebay.com..Mode: Default..Source: SAI..X-Cache: CONFIG_NOCACHE..X-MSEdge-Ref: Ref A: 5AEEB32DA75544D1B99F9E868EB35A7C Ref B: SIN30EDGE0313 Ref C: 2025-09-06T03:44:13Z..Date: Sat, 06 Sep 2025 03:44:13 GMT.....PNG........IHDR...}...}.......l%....pHYs..........+......IDATx....o.U.......K[hK+.....H)...h4.11.jbL@xQB0$.............O.x.D_.(..%..\b..!.!....sq~C..f....;...INgz.;3;.s~.2.L.....0.v.t..t..t..t..t..t..t..t..t..t..t..2....144.T*.].a..4M.....N..19y.P..p.^...H$.x<.X,...";...D`...+W.....x...............x.N. ...ipp.^:.bdd.N$.......3P^^..3g....UUU.5k.._VV.y......A&..O.:.../...c8s...E.k.....4q... ........{.....:......^..---X.`.Z[[...4.%.jtww7..8..7o.a...t..._ &.....Gq..a...a..uX.v.]..D`.S...w/..<...b;..l(.PA....#....}}}..u+......!.....w...C.......ezzz...k..$....@H..o...9......{.}..$X.<.....<EMAIL>....k.......".N......j....u.I....Xg....}.z...wz..S....*.t....ttt..}...={. .....&A...hkk.#...'Jwz.$.......N...d2i..O.>m...l.~.\w.....k....8q.D.m$..n....V....K=9.*.4.p..ytvv....9.......D.E%?.k....Y....,.....e..O....h.";...b......x.......=+.n.k.....Z.(.B9......+...nO.Qs...?{..H...V.I..N5..s..6l...'.qz6...\........d.'....(.MMMc.....z..%.._K.y.\..BN.......Y..f.....S.}"t.I:M...........O......_K...#y"......`..I.D......>...L/.......L....J/dM..-A...@J/t.NC.\.%.{H!..d...H..A|Dj<....5+...L .....S$....Q..|-...t.B.Mc,O....C-`ESBa...M.....F..T...\.>..#....R2...L..7...y._5..+...mI.B.}z...=...I3U4...gsz....2f..2....vo.l.=},..uN.....?:..^..!....~#.......].s...?.r..H.-4.p..z...m....W..........[HF.`...f./G...Y...-.e.......a...$..^./. ..4C.4y.T.@....3......'...Gm...5...#'x.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.Hg.G...:i.HMg......I.....`..\.....ZU..I.^4.a*...1.CqO....h...6....'...)...*..Q]..'.w]zda."..1|.7(............b.'.......b.._....%.....F...9._.n.wo*.'......_.#..wV...m.ALSGHQ.d..h|.C...M7[Q...#h7oc....L.Jdz....1L..n`q..X1.s.j9...J..../7Y=......~..=......p*.....y..Q.p,.].....4.).....j.Z....?t.iH_Z.)..m.......y;....p]-..lBtI.2.\..L.L..........7..\9'.T17^..U...w.*.N.SnI_1...U."..CE.",......x)..|.E)....w.u.n.3.w.......[.A........[...... *.. ..M...../u.L..../.x..m.:.....2D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3D.3.?ec.....?....IEND.B`.
2025-09-06 11:44:13.178 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:13.224 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.224 | INFO     | __main__:on_message:235 - [HTTP_send] *********:39543 --> 13.107.246.50:443
2025-09-06 11:44:13.225 | INFO     | __main__:on_message:243 - .......tLf......t....Bxi
2025-09-06 11:44:13.225 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.drainOutgoingQueue(ConscryptEngineSocket.java:632)
	at com.android.org.conscrypt.ConscryptEngineSocket.close(ConscryptEngineSocket.java:539)
	at com.android.okhttp.internal.Util.closeQuietly(Util.java:91)
	at com.android.okhttp.ConnectionPool.cleanup(ConnectionPool.java:288)
	at com.android.okhttp.ConnectionPool$1.run(ConnectionPool.java:99)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:13.693 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.693 | INFO     | __main__:on_message:235 - [HTTP_send] *********:42812 --> 202.89.233.100:443
2025-09-06 11:44:13.694 | INFO     | __main__:on_message:243 - ............~..|...5w.X...t@NT..@.-;uF...{. b..".,......Z./2.......A.E...>.\.".......+.,.../.0.............../.5..._.........cn.bing.com............................#.........http/1.1..................................3.&.$... .........(....)0......8ss.%h.TR".-.....+........)..........%B Y..gI...1.....:.8.@...i...B,..#E....#H..!n......d.K$.W..\.p..Z[....\/nvkf....I+;...kE.).,'.'...^ .']...... .e........1.p......^..S.t)...{..lc..n.4......W:..%.%.}..c....,...r*f....................CL..=........W.(..d.ZR.^.1.....y....5.S.<.\...=..H......9x2..........|.l=...;-...}V...yM......tgu...C.......;W-...E./..V..)._...T.i...5..i}.^S....)....$..x.'.W.w*..3..\.U......KvFQ..d.X._}n..10;......k.F..._V....95..T....}...p....7....X.?.T&
2025-09-06 11:44:13.694 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:13.706 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.707 | INFO     | __main__:on_message:235 - [HTTP_recv] 202.89.233.100:443 --> *********:42812
2025-09-06 11:44:13.707 | INFO     | __main__:on_message:243 - ....X...T...!.t..a......e......z..^......3. b..".,......Z./2.......A.E...>.\......+.....3..........
2025-09-06 11:44:13.707 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:13.739 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.740 | INFO     | __main__:on_message:235 - [HTTP_send] *********:42812 --> 202.89.233.100:443
2025-09-06 11:44:13.740 | INFO     | __main__:on_message:243 - ..................~..|...5w.X...t@NT..@.-;uF...{. b..".,......Z./2.......A.E...>.\.".......+.,.../.0.............../.5.............cn.bing.com............................#.........http/1.1..................................3.g.e...a.>...,...=............G...}jh.....D.B......./....r5.....j.b..av.a.UGy..v(..B...%..*..I-..]<.u\.p..-.....+........)..........%B Y..gI...1.....:.8.@...i...B,..#E....#H..!n......d.K$.W..\.p..Z[....\/nvkf....I+;...kE.).,'.'...^ .']...... .e........1.p......^..S.t)...{..lc..n.4......W:..%.%.}..c....,...r*f....................CL..=........W.(..d.ZR.^.1.....y....5.S.<.\...=..H......9x2..........|.l=...;-...}V...yM......tgu...C.......;W-...E./..V..)._...T.i...5..i}.^S....)....$..x.'.W.w*..3..\.U......KvFQ..d.X._}n..10.|..i.c.xh..kVj.w$.g.).y.M...T".,...N.4....V....
2025-09-06 11:44:13.741 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:13.812 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.813 | INFO     | __main__:on_message:235 - [HTTP_recv] 202.89.233.100:443 --> *********:42812
2025-09-06 11:44:13.814 | INFO     | __main__:on_message:243 - ...........9v.|....u.q.S.....4.-....Y...:.. b..".,......Z./2.......A.E...>.\....o.+.....3.e...a.T...,.......)..*.'...+JE..t.Q:].....d$..0.....W.#X{d.......d..P....9.......C^Y...J....!@.0Dd.hh}.........O...(X.....G.'.^....*..[:...t.M.....!&!G~......8h&....:....?.)*......c.!.+.U;...S.H.Jc...@ ..F3.E_.c{.....}.. .N'A.l...D..kdaC....RP.t...q.w....)N..........E..........Sh:.......M>..)v...&...f.........M.....X..#...%\D....i.N!.BM..f.*.<...M../cX.pz.x.. -..P$..7l..#...Cu.......N)...`.>...../...(....Xk.......S..c..Q+.LPm.,.,..{.g......S.....3.....7.3.M*..~.^....q....f.K.....y=.b.....\.w:......y.....z.r8.......+.Z.../a/n..Jh6.....MO..F..qF?....Z.......A.r_j...,.jI.,NX..\............*......G.c.D.{....3.W.#.p.......=..b.}5.....v...x.g...-...gS...T.......|..{.......$r%.c:.u`BW.........MV...>.........2.`g.z.rR;=Q.G..o..{p.V5.....t.Q*...a.?y...x..s...).....jT......Q.......l.K.ER...1(.I.|r.'....}.....O..<h...>:d.N.W.sq..R'_#....]...........R....E8 ..8..t..23...m.....i.....,W..m.2........lw....w.i.b.djE....1..0.g..@....7..^......n/~V...........>.^<EMAIL>..t.M........|$.;.....Zq...:...'.i......7dh.n1J#m;.jX.S....H...z.....(..#}.....|..6..........^.6..".$v.2i.....E.......-...C......[[.7.CfEb...m..D.3.X....O.(....L..M.r.p..1#t.....^b.i.l.../...r4e....Q{....H_.e...|..}c.E.."..@.V.D.QQI[$"@.Rz....p...6SI..;^h.\.xX.TaT..(.;q.....p%.(..B.......Q.".P8t|.k...|F.Vh~U.J.-..0..%@.|-...'..{c......6.=a.....},.-..P.....@p..*.
2025-09-06 11:44:13.815 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:13.926 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:13.927 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:13.928 | INFO     | __main__:on_message:243 - POST /OneCollector/1.0/ HTTP/1.1..APIKey: be6e4c19699f4fdf9f8c0ec9f9b398ef-25d58bb9-9926-4df7-8e36-bc7afb4b2c34-7563..Client-Id: NO_AUTH..Content-Encoding: deflate..Content-Type: application/bond-compact-binary..Expect: 100-continue..SDK-Version: EVT-Android-C++-No-*********..Upload-Time: 1757130254560..Content-Length: 782..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: mobile.events.data.microsoft.com..Connection: Keep-Alive..Accept-Encoding: gzip....m.Ao.6....Hl...vmW..."=...RRd).a........m7.".....RT..4....o.|...5.i...}.a.M.. Ex....{...=...$.f.9..H..T........~.....O.~.>x..~.&^".0.8..&a...$.l....Y-...d*....h.....B.....a.|...2.<.3../.F..J.{v.r@.'V.|s.]lQo.q.O......Z...G.\ev&.V.J..Y....6uC...G.lJ......~...../;7...3..u_.... d...HT........xm..ww.Ta....Km...9......0.,.....`.>../.n._m..$..........u..:....1.<..!.?of.K.}BP..j../6xU..._Y-....GE...ZNY.r0V.L...B+.'...T,Sx..}89..4...8....m.{Y.^i.@=...r.xS.....s...J.bE.......O....f.dn.=x.u.....+;..b..,..G...zt..+.!]mS$d;...}z..h....\.......H.....A.....Gc/q.....)zxC..a.*ew..N'..h...DU...l......`...Q9.*+.....g.......~.v.'g...3.s....J..B:...|.......9W...J\.......)jK....7t.%.c...{.0.1.<....$.._..+.).n...QG.......)-!7+'.S(m4....J.#.]U.1..&.s.....t.*..y..).4.Q%..jn3.YY.H...p^.W..N4.....
2025-09-06 11:44:13.929 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:14.103 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.104 | INFO     | __main__:on_message:235 - [HTTP_send] *********:45368 --> 20.42.65.91:443
2025-09-06 11:44:14.105 | INFO     | __main__:on_message:243 - .....5...M....:&uZv.LN...H;C....k.}.....B..U+.R..x...6oW....[R...SgN..,TY...f|..h.{........uU]}..4$.O..#./...6.41........[vpp.".........n..t>...x....x..z.....sQ...c.., .Ofv.i...i.+.o..z/3..E+......vn.L.o.NB.7..Y.h......+U#.O.W........9V(.tfI..P....`.!....Y.&.R.j..qRR.FK...0..A...Kc....g..(.`.W.n.. />..S...M...6>..Nqb.:rZ.%.5>O.p4...-..W.rV......M....<,.V.c..Tt.....wJ....0X.._...rWF....+#.G.>.............,.O.Pj<k.G.X ...d.... l.>&PI.....R..RE..ro....X5.h..W..........#g..~8/a.....,....]....Jp..DdMW..]...`..+mEL:A.5.7{CF..O..1m-6..tua......C....'.A.g....P(..cWhjK.^I....N..r_......{.`...f...p..c..."....FLI(g.....W:.9.PY.v...:s...!..~,.~9...C....I..].B.=.d../9{.0'...K..Y..F.!...^...n.|^..<Y..h..}cq....W..@G.%. ....-"[E....&}.z|r.I................r]:..$.....9....|.'.B.....4tf.._.....9[.5.......p.x_.]*......)^.P:Uj..fo&.F.%s3.....9...._.]m.u.z.z..V!P......0X.?.Rd'7.{....S..)....".Ji..W. ...N.o.C..5..!....K.L.4...~$l...W....o..l...0g...J.e.>P{%Z.Q>].*......?CX.%...*VY@iq0}P...u.8.....|>8t....|H...+..+.D..*O......8x).R).Dr7._L..f%h.1...ZX..v../..e.K-...A2..n.........z..J^.#..d....Dz...J...\l.R.....P.v{6;CEo........}a5..|h.S.gKm..J..W.7.`..+..Y...x....aE...O}...h.........Y.h}.'U73.P...&`...g....QA.C.C.w.>.j...j..C...~...-....X^...'.........,{.k...h..G0!......+*.mi.....#oR.QK..0.S.l..5.
2025-09-06 11:44:14.105 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.185 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.186 | INFO     | __main__:on_message:235 - [HTTP_recv] 202.89.233.100:443 --> *********:42812
2025-09-06 11:44:14.187 | INFO     | __main__:on_message:243 - .s..I|.HBc<....n.....$.L...H!o.8....&.5......j...P.....`2k..tk-E..;...E.c.=............p.:...0_..-r-..V..aL0.G.".S..".1A ^.(.....J..j.2|X.L.$=@.l.S........W......`s..O......J..........)F....()..D%....\k...)...;jJ.hQ$.D...!kT+.....L..L<...p?..U.0z...........85..2d.wS=...=......k.6BI..>..1r.w2z.0...e...*.>&...l*6%x,.cB.}:...ln..\.6./.3D".....<<&...-..K.....'f....3.l.&b...M......5..i. ..z..m......*.M.6.B[w...)....d[.....'.%H..=...........*B.r,J.......2.t.F..Q.fK..g.....l..3.u...u.....C.=...U......#$<.o..+5Y.X3Z%3..X.......B*)vh=]......_A...U@.O5tGf./.~... .A..o..i...`IS:.9.O......Wh.gG").b/+y\>..pc.j......@..V..Q...]:..6c...WC.c?F..K..t.3.4..X.E|Y.*E9.1.68"oi.z..".?..^../X1G.c/..O;....`.]{5wU..h......].nU.. E.;.(:..f..........r#.$d}..>.Z.%...0.u+...X..:.pa...2..r......<n..g&........<.....i>....k..P.g...1u.....R...2.V....+^..N.!3..dS....R .LDE.X....|..D!.Fl.)...a.Q......ZG...3'...7...J.....{/....Po....-.F...a.v..X...,....a`....::w*._......<.Ft.u$_b.......q.......I....K.....aB..&W....w+FZ.%..G.Y...@..).N/....\I.q.?J.....5..Qn...m....!g.Wo.....KH)\u}.s]....b..-j..h.!.S.Vz/n...u..7.X..m.[.`:.2!7.~M..O,.M.G(.oV.C..`"..$.w..........._...4..R.......V../j.J..#[......><8..|...*n.]...+v_..*s.r=P...k9m3~....g.u..W.':.......j..kNI...G...!n.)..9}..`.<C.xw...8.?H.og...7R....;..8...m1..~7..?..}..t.cib....p..C_...+3..I.3..H.`H....\.5...z.Kn.........nVOCw......V..<N.A.G8hI...j....a.....5q..k..`..6....(89.8.@.6..K....k..}..................R.}.....Fw..'...:.-..iI..Y?<.A...2o..........?..P:.?mp.N.k......_..@......O$!V.{LI...\......(...0..y...r.QLF............&......H...e.kp..s..w6.]..).Cyx..;....^....5EA....fa.2.!Puue..........o$cN.el......f.7....<..N.C?.....B.9.t...u6j.QD..f.......!...m...2.N.............JI...RZAU..2!;..n.!.'........Y]/..)......x.....U+.)........B.J...W..3...5r/......+..L.]70.O~....).Y.eG...............aD...SS...&........=..8.[.x;.....o."...Q.j c..vM0...-aj...#..q7...1.m... .M....MA.Hn.8...)(.Y..V..M.x#..yR..\A....1E..<..W .6...>vj..Y..!.).a.......>./..M....O...8.5....[j3..M..}a...G...&.!7..At.*=..J..5....r........-...r....^I.2.....+R.lg.....IM./.4..M.W..y... ......PWb..?..iG.....#aK=.=A.V..i)>!......\.....D....f........Yi".....*x..Fw \Q..3.=.Q.Q.<..uy.....\.."-..(.]...h..)...c|p..g]O......5..<O..-.K*..<EMAIL>#.g...x.......g../..{....$...H..s,{. Ay.f..Z....(..=w`.!....h.......B..=d.=..QA..W.....~}_..".......O.|.K|w....{..p1y..C....;..el......y....:..&........c..H~....x........ycY.@b....<....C.x..,...;.$...W"..J...E.@NW.j...~Y.r.=T.C....U.8..[-.4&.c.............Gi..g...UJBvaT=....x....T8.M..<......ea.|vx'.:?...qR.d.........{.GT........Y.l.U.>.9.k.CA....Xf...H.S..O.7....a../X..wp.C.tZ^!T.!....1Bz.`...@......;...=#..'^G...o.G...C..p`.m..QT|.R.....z.l..q'.N+c.z..U.;Y(.,2.....h.M.]..Z...@..............w...i......U.I.k.'r.]hy...].) jx.9...k.M.u...d...k...N\`....q.R."..IJ....f..O.....7.W...VL.6.:....qU.e...c.>{.v...z).#... .^.W~...z"K.L..l..h....$....Dv.l.hp...Q.a..a{.#..,L#...cN3o.....`Z|..c.:....-.m...4.N8..c.. b.b-..+...-...P.}....E....b$1..W*j.d".3........J..W/..d*7....^..A].C...).<EMAIL>...2.E...."..v..Q..#..g..........(.E.+.:..C....5....C.h...d.c.....'.)%....e.......bi.k....{..............9.-_.e<.~......S5....J.....U'.T6%/..1!*.%..t}..S.J.%S..B...:,..q...R....aR.3.X.S..:<EMAIL>...D..0.z.w..h..*.T....B.UNx..o...5...:.G....tU...4i.(.....`..)..4|`..4d..2v.V.).7.........=..M.1K..rxN..H.[.<..WFd.....plA..Yu.......Ee.....y.....8.m0/..[,.=........5.C.-y..}....ce.|...gd3.?.:..K.~.......+.|...Z..t..kc..-a...Xx.e.z.....N...._.. ...P...Q....c.Q.....s..aS..].#A..5..........v.:NI{.....+.*....~>.K...0.a..`PW+..G..../..c.&+..x...kM.}....5...H.2.)=.$....&..U...?.(..Y.;.....2zP.F...E.H..X..#..Y...Q......85...@...r.\..J.^...z......^.$....:iQ.p.....|...5=.f..1...2..k.(i;.......Li...v....c..r...Q.u.Ht.r.jP......3..._......q.~.%....k1.W...S>or$&$1...gy.......N...M*..XMo.......q.+..F.......$..W...&.k.;${..R.>Z..g.q...p.....Y...t...3...p.6g&...|....X.D.<$.x....o.sA.....tqk.\y........b.ZY8D.2I.(.s.K..[...SD......1.$.......^...dw.e.."...^.....[.....vqa-..8,P\d,..nV.v2....ql..V}..sV.u.y.1..p..uO....X.x..".Q......W... /..L.}wT.9.==.......d)?..e.......{W_...F[._.. ..v.>..aG/..!.h......\K^.pU..\].E....$t....(2.+Y....L.5.x.4.5|....G..u<..}/j.4.W..V...D*...J.[.e.k'.w..M.'_C... )p.BM.......x$.1..o<...!}.U.$@....v..}P.\.....hfrWB...q.....h."F.@...3)..P.2.`..I>...V5.s....3Tl^7..,.{..#.....P....t......."..A..l1...{..F%...9P.....7.%ey[....r.Cs.h...j.L]..{..f.J..r.....lD7Z.......R...E....{B.(.3>....".wc2.tI.m..O..Ww{.....|c..&q.I.{.@yF...@T.(h.. e\:...0.kh......tz.A......h.K...7:.ms..Z*.[b.X{_..[.l..w...N.kB.{s.&D.u...{'..3N)SU......a...|....GZ.....E.....8.....<.~......91..vB...A....\..T'...q.1S#.x.....(..}M..p4.e+56.m.Y*..~...b...+p.|.J@X7....3.I.1....F%...7t..Q....Z.+..JkQeZ.....?..*..dH.....hqQ..(. .M<.......g~....&...1....\..-.<...<...}...v.*.e.iO.Ez....E.7.j..r............j....b...2.........W....u..b&(....|0.PO..~.3@..j....L{0....N..H.5....56J.m..,(.LXT.6.fD...(.......LL.....e.G y.sVgNP..`..:{.0z....e.u....2..6.>......./....mt.yl,2..e..-.@.$].T.p...._....L.,..g.{..S.......*.0.q.!..........= u....0,.b..x......P.Un.:.~S..........P...R...?d....y#l.......0..@.I.(..].....I..o.rm)'.[.-2.>....SH..).Nt....aK0&.........P..%.}...c.>...;\.?N].W'....ZVJ.:$c....9..r.n......a.Z&.nAcI.......@T.8... D...XN...K...h......]{........;".........w......h...OT..~A;..&_k..B.....*.%]....{gOz.......XA..-..h4..F..L5....N.i.......RcE.Z..+.z<e.........x../XD..$...J.._..+o..c.mS....l.....wHm.,..|...2|Q4+>]g..:....(6O.c=2......?."...A.c..>]L.1LU.:#.|#...w>..pz.9.....=....qP..+..?R...<..E....T;tS'..............D........R...G+..yC|.J...]s.N.c..5..Dt74,....3..'..$....U..p&\.......#.aGg..kE...5.7h.*>-..c..h.D..TC1.'.j.[m.T........@.".>B.d5.mS.J...*OK.A..$.kZ..c...<.5.\./<.%T+....f....jf.T(m.....h|V..I.wX......Xf..._.b..\.;4..f...LKd.G8.....m.<..~.[..).....B...1.k...*$.+....#..8njn.....{.\./
2025-09-06 11:44:14.187 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.204 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.205 | INFO     | __main__:on_message:235 - [HTTP_send] *********:42812 --> 202.89.233.100:443
2025-09-06 11:44:14.205 | INFO     | __main__:on_message:243 - ....E.y...}........E.f....T7.=4...M.e.M&.$/..e.....?k.L.$....R.Q-G.......9
2025-09-06 11:44:14.205 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.206 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.206 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:14.207 | INFO     | __main__:on_message:243 - POST /dict/fwproxy/receive?sethost=gateway.bingviz.microsoftapp.net&isHttps=true&app=edge HTTP/1.1..Accept: application/json..Content-Type: application/json..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: cn.bing.com..Connection: Keep-Alive..Accept-Encoding: gzip..Content-Length: 1203....{"dvid":"309091B7792E4C3E98ECC394387E41C4","events":[{"id":499,"sid":"1CF017E82063467991E701EBE24661F7","name":"FirstIDADAction","type":"Diagnostic","ts":1757130254244,"data":{"IDAD":"true","IDAD_actions":"click_omni_ntp","adjustId":"E22AC3DE46FF02ADE666AFE5C3E122F4","anaheimId":"-2588324515577707453"}}],"meta":{"appPackage":"com.microsoft.emmx","clientVersion":"139.0.3405.111","build":"Google_Production","market":"zh-CN","applicationId":"5B0A374023184095B4283E7F1ECC8DFB","installId":"-2588324515577707453","os":"Android","osVersion":"34","timezone":"480","deviceModel":"Xiaomi MI 6","locale":"zh-SG","deviceType":"Phone","installSource":"google-play","launchSource":"LAUNCHER-OpenURL","extSchema":{"user":{"anaheimId":"-2588324515577707453","adjustId":"E22AC3DE46FF02ADE666AFE5C3E122F4","userSignedIn":true,"aadState":0,"signinAccount":"MSA"},"app":{"dataConsent":"On","installNetwork":"Organic","installCampaign":"","installAdgroup":"","installCreative":"","isDefaultBrowser":"false","detectedMarket":"zh-CN"},"device":{"tags":"hasCP=false;isMDM=0;isMAM=0;isSDM=0;isAWP=0;isCMVPN=0;isMAMT=0;isMSAAllowed=1;isNTPC=0;"},"ext.req.requestId":178},"first-install":*************},"schemaVersion":"3.0"}
2025-09-06 11:44:14.207 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:14.304 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.304 | INFO     | __main__:on_message:235 - [HTTP_send] *********:42812 --> 202.89.233.100:443
2025-09-06 11:44:14.305 | INFO     | __main__:on_message:243 - ......IK"n....>V.k...!\...jx@.../V....&z.o..(...g($....o....Up..}~.}.4..@...[P..\.X.V..m!..U..;..fH..]g........G...R5.....w.2...X.....PH|..>i.E*Y2...t.....>i..]...U..6.k..E...5.....!.%..P...,...../7.<& .#..$:Z......2M.2...[R.I<.t..5..}.:,@W.e.T\9...p..F.M.+...Y..R..a..f.acz.N.....f...().....$..V......H....D'.UK.._......i-UY..AB..YU./..*U.y.0{..........n..D8q.w.-.g.?Px.s.lD..f.O...=N.4G..4.....e..m.z.....E.xLP^.....:......`D.......A4.+..N.....3oi.}.:.l.>.W.EYr..>..9w.ha.......V.Z....2.....L.R.........v....^S..RFT..*......?GJY......+c...3..BC."#...7...F..L>W.^._...=.<EMAIL>.Q.......H.;.P|J.....c...+..k.c.9...p.x..VA....0.RG\...z...^g.+6QS.}.k.z..........d.;.Xzi..$.......V...1p4{.\./..[k.....o#p........D...c..e..K.....v41O....zB{5#0...&j!.....D.......sK..M........W...[V...Q....o..y......{y$.6]...<x{.!._9..|.U..e4j...4...m.l.......M..e.../..-.i.j2Nh.8..#.a.n..*...&X..g...@.d#P...p.8...b...<....Y....,.....Y.....$..9*..ql....B..P.....P..V....W..SW.}..q....)..|K.j.+L.....6.....U|..tx=..N..Ph..n...".....-<...?rv.n..%.;.H........in..........S.b/I^.{.../?..LO.<....{......Ep.WW..NW..hY.U..&:..P....;..&D.P.X|Nm%..nX.*T..p..tXz....................M.@.k4.R...>...[._..j...<^..t?sE....o.........K7:>...Gs,b......P. .......K{.*.........&..!A..Z.H...k1.SG......k..7!t.....8..>~...-....J..".X...._.x.,...#..:....f...bqZ.6...|....bURdd.>.GO.+..a.f.>m...J..f....L.H....oYW}.....n...^.g.....1..=E.O.......YaD..vs.._.M..........%Mu. &....|...Bt{O..x...I.."q3%1.R...1J.j..n..P#B*!y/...f."..wU :....h`.GI....E..}.i...K....{.=....P
2025-09-06 11:44:14.306 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.315 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.316 | INFO     | __main__:on_message:235 - [HTTP_recv] 20.42.65.91:443 --> *********:45368
2025-09-06 11:44:14.316 | INFO     | __main__:on_message:243 - .......'~.b/....]Q."....q3..`u/p..u+..../.c.Z.wh....R.f..P.y.v.R........m.o.'.$...w..8....$:....;l5..8.f.....*2'. .g....R?...-...qE6.YM>.....}.t9...B.+..../Q({-.`QQ... .......${..].-...U.....IT.....TH._...;h..ry.*.'L^.....%/.\...._e.e.. ..^u.7...?.(..;..<.B.]h..^a.9.U..:......>3....S..4)..9........j{?_.'.G....}.A..K=...`.&i.u......NQ.m/....k/...U4n............&.zd4[7......1*.....l.e.......H.4a;....El.......(.S..P.....s. w.5........J.....%A..
2025-09-06 11:44:14.317 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.317 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.318 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:14.318 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Content-Length: 9..Content-Type: application/json..Server: Microsoft-HTTPAPI/2.0..Strict-Transport-Security: max-age=31536000..time-delta-millis: 145..Access-Control-Allow-Headers: time-delta-millis..Access-Control-Allow-Methods: POST..Access-Control-Allow-Credentials: true..Access-Control-Allow-Origin: *..Access-Control-Expose-Headers: time-delta-millis..Date: Sat, 06 Sep 2025 03:44:14 GMT....{"acc":1}
2025-09-06 11:44:14.319 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:14.339 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.339 | INFO     | __main__:on_message:235 - [HTTP_recv] 202.89.233.100:443 --> *********:42812
2025-09-06 11:44:14.339 | INFO     | __main__:on_message:243 - .....?/Cp..,.qE.9.=...f.....3..&.FE..C..D..x.f.v..I}....`Iig.._.7.Z.+..b~....+....`o.i..A.'...!..m.*..WZ..m......d..l] ....SE..g....D...a..9..C...D=8c..j...r.H>=6i.E.o3.C@..G.[F.K.J...&..`..V..< .E..'.J.u.....\8..1..&.b.....R`.t.;%....6.wb|...wl1V..X....  ....^M=..~r.....4s......lG..Fi'..x....tF0)....h..3.......".......H.ph..7..s |mi.Y.0.l.D hCDx....e\0/...G.....,......K.#u.........9......b.(.<EMAIL>...8.M/.T....5..........4.HK1..~.>.n. ..i......N
2025-09-06 11:44:14.340 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.432 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.432 | INFO     | __main__:on_message:235 - [HTTP_recv] 202.89.233.100:443 --> *********:42812
2025-09-06 11:44:14.433 | INFO     | __main__:on_message:243 - .....$...]..#..cb~....L.I..G.....]......e..j-S\...Y.G....(?~.1.r.w....O<..!*.^.O..:.[4...........F....1W...l..........~..:5.%...Op...8...eX%.&e......r.0^7..E$6.];*..t.....[(...'4%.}R...X.)...YDC.T.....`$..7.=..h...4...)...e.l<......... .....X9.C\Z_..J..j?6...rK..6.......k.......Xe....~..4t........r.#..mAfy.[.4|lb.c6E.R71......... ...A......O.......3!`*P.z.m.$.../hlVU...@e..=..........(;R[.^...[B........L....t...B..F...4;.%5.\.....$.#6X"v...:.(...W.l...t.......n.(...2^..X...Q..L..lyUz.`...x.....zh<..p...6... .P...&aU_..=.^...6...<..8<.L..y..bK..n/.....*a......3(.Pm...L.%N!.2......l.....I.s.../.v.g..CF..F...x.....p\.]<EMAIL>.T.....7.{..........1v.....L._...f........sWc....:.G.p..Z..J.z|S.bz1..NE.9....h..oi..1r.:..pJ.+..8..{..y./..9...T..#.c.........F_L..=.......7e..3........D-..8%.R..c..[.......8....G..e.B......%.."b|XSRR.L9.M.x..$iW.e..q...4.0%VI.PL.t..a.8...F.z`..&*s.^.I....UR(...)..C.}<EMAIL>...(...<Sc?j..?...8.......S'.....;.....C.">.z.K.E.K..I..hr..e......].<..7.....Y.f."...$fs..O7.C...
2025-09-06 11:44:14.433 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:14.434 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:14.434 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:44:14.435 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Cache-Control: private..Content-Length: 15..Content-Type: application/json..Accept-Ranges: bytes..Vary: origin,access-control-request-method,access-control-request-headers..X-AspNetMvc-Version: 5.2..X-UA-Compatible: IE=edge..x-azure-ref: 20250906T034414Z-17f8ccf5d94fkvq4hC1HK1ern400000001wg00000000a9x0..X-Cache: CONFIG_NOCACHE..X-AspNet-Version: 4.0.30319..X-Powered-By: ASP.NET..X-MSEdge-Ref: Ref A: 3F607C29924A403FB04C6623730CEAB1 Ref B: BJ1EDGE0507 Ref C: 2025-09-06T03:44:14Z..Set-Cookie: _EDGE_S=F=1&SID=257EBFAF40BB64D710C9A9F04195650F; path=/; httponly; domain=bing.com..Set-Cookie: _EDGE_V=1; path=/; httponly; expires=Thu, 01-Oct-2026 03:44:15 GMT; domain=bing.com..Set-Cookie: MUID=********************************; samesite=none; path=/; secure; expires=Thu, 01-Oct-2026 03:44:15 GMT; domain=bing.com..Set-Cookie: MUIDB=********************************; path=/; httponly; expires=Thu, 01-Oct-2026 03:44:15 GMT..Date: Sat, 06 Sep 2025 03:44:14 GMT....{"status":"ok"}
2025-09-06 11:44:14.435 | INFO     | __main__:on_message:244 - None
2025-09-06 11:44:18.439 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:18.439 | INFO     | __main__:on_message:235 - [HTTP_send] *********:48572 --> 204.79.197.237:443
2025-09-06 11:44:18.440 | INFO     | __main__:on_message:243 - ............9.j..=..\.g.U..M.t..D...%...{>Z .I.....F...:%.g.]......IO..).}...........+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... 9.p{......Q..R.QlO._......9....*.-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:44:18.440 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:44:18.600 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:44:18.601 | INFO     | __main__:on_message:235 - [HTTP_send] *********:37136 --> 13.107.21.237:443
2025-09-06 11:44:18.601 | INFO     | __main__:on_message:243 - ...........T....<.2;4.....t;E#......V.....y H.=......p~.:c.Z3...7p.`..../............+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... ..K..^7r.....d...N||....yt..... .-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:44:18.601 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

