// ==UserScript==
// @name         SEC-MS-GEC Token Generator
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Generate SEC-MS-GEC token for Microsoft Edge
// <AUTHOR>
// @match        *://*/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    const CHROMIUM_FULL_VERSION = '130.0.2849.68';
    const TRUSTED_CLIENT_TOKEN = '6A5AA1D4EAFF4E9FB37E23D68491D6F4';
    const WINDOWS_FILE_TIME_EPOCH = 11644473600n;

    // 使用 Web Crypto API 替代 Node.js crypto
    async function sha256(message) {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        return hashHex.toUpperCase();
    }

    async function generateSecMsGecToken() {
        const ticks = BigInt(Math.floor((Date.now() / 1000) + Number(WINDOWS_FILE_TIME_EPOCH))) * 10000000n;
        const roundedTicks = ticks - (ticks % 3000000000n);
        const strToHash = `${roundedTicks}${TRUSTED_CLIENT_TOKEN}`;
        return await sha256(strToHash);
    }

    // 将函数添加到全局作用域，方便在控制台中使用
    window.generateSecMsGecToken = generateSecMsGecToken;
    window.CHROMIUM_FULL_VERSION = CHROMIUM_FULL_VERSION;

    // 可选：自动生成并打印到控制台
    generateSecMsGecToken().then(token => {
        console.log('SEC-MS-GEC Token:', token);
        console.log('Chromium Version:', CHROMIUM_FULL_VERSION);
    });

})();