2025-09-06 11:46:31.830 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:31.832 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43230 --> **************:443
2025-09-06 11:46:31.833 | INFO     | __main__:on_message:243 - .............E..E......M..?.x.DfS..!z.@#.j. ..4......:.p....Y'..C....t..W#...".......+.,.../.0.............../.5.............cn.bing.com............................#.........http/1.1..................................3.&.$... ..f-Qd.VQ<b.i2..D....q...a...I.Q.-.....+............................................................................................................................................................................................................................................................
2025-09-06 11:46:31.833 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:31.842 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:31.842 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43230
2025-09-06 11:46:31.843 | INFO     | __main__:on_message:243 - ....X...T...!.t..a......e......z..^......3. ..4......:.p....Y'..C....t..W#........+.....3..........
2025-09-06 11:46:31.844 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:31.869 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:31.869 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43230 --> **************:443
2025-09-06 11:46:31.869 | INFO     | __main__:on_message:243 - ..........L...H....E..E......M..?.x.DfS..!z.@#.j. ..4......:.p....Y'..C....t..W#...".......+.,.../.0.............../.5.............cn.bing.com............................#.........http/1.1..................................3.g.e...a.....$.S.......0...F....-.......u.~./.E.....0....Y/n.c.>%.9'...j:..z.t.~.......5....f...3.c`...1..-.....+.......
2025-09-06 11:46:31.869 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:31.928 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:31.928 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43230
2025-09-06 11:46:31.929 | INFO     | __main__:on_message:243 - ..............Nh...G..Cf..b..iv."8...y..e.. ..4......:.p....Y'..C....t..W#......o.+.....3.e...a...A..eZ.a.@qUq.../@IJ..}.z..h..Vii...M.k.^.#.f.b.IY..]..M..r.Y....fs..1.t>o~1..x........M_..%.Ni........#.n?..c..[K....*y....n.(G.4S$..b.)z..DH.|%.x..v.7>0G.......a].p...Sg|......Wld3n../..*z...F...X..n..'.%.%.?.\......q.{%S(...9.f..7hm+....j.....#.9.+...{=.o....?.HBw%.i..*.n..0.(.P.|....Ow...&...~..5B~...."z.S...w!q...Y....F......dc"....8.V.GU..I..?^......_~%.......Qm....1\=...z...u......O.<2.9..)$..b/..T..........y.,....(...)."..*...i.J.V..+\.OW...\!c$dU.U.q..._...l....rK.~.v.....d.[1..Q..o...Y.m.<.._@..o.]...W......;.:dE.7.J.....w...B.v.Q..6IB...TO....m..{.......r..2..VR..+...$................/....q....$.*....1j35^....,........"Wie3.%....].......R..".Bma}........s.......Q.9..`<...m...).+9..2c..}..w..(M.8.\....^...?4?..78.$P......Ut..C.....J...wtc.)v\{i.....|.v.6.T....m..D..........TL.U..{..<!......x..X...:.....+,)..';..r.5..t1(r.....8t..G.<.../r...\.....h.]i.F@......i:...:...AkkD.I...{....%........@P.,m..E...Y.a]............J..<Un&..g/..................b....D0.0>S..t...&.Z.....V;Q...y..6G...H9(..K .....d......c...vP.^.....)v..I.....I.@....8....Z7.S0#.V/#.K^c9s.sr.!.g.Q....#7.@r....-.W...6.!T......x.?.....i...)...>kV...Py.Hk.}..6..k..?.".a.....YwY.P...,...U.&.}s)..e..%'..2.UT..B... mq...v.*.-...W.*o..f..:.&W.qp.'.L;....y....i...J<..6.2..q.E.sK.]...9s.M...."Z.....X...!..Q4J..:x[.R&!..PC..9.....d+..H..H....x.\9 xnb.....c...H...d...!\1
2025-09-06 11:46:31.929 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.157 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.158 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43230
2025-09-06 11:46:32.163 | INFO     | __main__:on_message:243 - ..2I..e..V..?k.O...G......X=C...5.=..".Cd.~.]%....O)..(..8.o....cv.r.........p0......r..~{..+nL..eN....\..!vF....E.."....._/...5.+.."<....{...E....r4T.d..b.h.<.Ch....d..q..W...XO".,.r.....,d*3......K..g..5.*uV...3.Z.3..>I4dRT<....zQ..#..$Zd..$...?......m..'..^u..~....Q..p.f..2:..t.B..\.0..jq*.:..~.@....a.#.+.h.S ...._..`.f..=W.t.._...mc.Y:..J....B7 .+P.Gz..e.@..*wx..g.p.....)/...0#4..3..X~..).0......0e...iP.P...._.?@[..W.x...:.l....f...'[.......n.].....^.....e.k_....6n`..6)^.Q.....*....1..jr....d.6G.oFz_+....x.D...q1...Fh)...%q.Q..=.,..w.....y.6.......9Y|..(TO...4U..fB....r[..N~.g....'J.c*......A.....a....K i.;..Q..a...9'..V.)z....N..c./HZA./..5V/..,Q......{.*.Q4O{..]..!.'Z.?.!fm..9.$..".......k...4...jD"..H,Z....'....Sd...aR....t...I..GfO.W.o.c..`..3...z..M..o.B&...mz.,@...d<1...t......~..!F7gz...2...S.......e......Wg.{.....s%..3th.. k...~...Q....;..`Y_eI.dAs....9...6X.....|...*.{.......l..@{_.0!.........S-'5w.....z......J...dY.Y...i.}0......D}..|....jwz.=......#..#.?.._.'}^........T.]7..>..]Agc..N.../"Q<M!.@.z%....}..lc.."O..2>.Y6Z...1A.s...........,..WQ.J..k||..(..6Z\..c...b.M=.4.'........K..ZD..m\..E....Ylv..._Z@Y?...{..)9.Sk_.....<......!W._R.......u.mO...S$.~...n.......\.]$..}.b.).s.[..}.^.....d....A.~6...:...f......N..Ory q...o.........B.%....v.E.\.f.L.....G..T(j/;s..eA......-_..."............H..1.. 0=._./.... C....][f.I).W(.D.Gsg........a.s.2>(...vs..S..$B.>...x...d ...6i.J..k......{...$...V .^C._.^....j...........G.+.....f..2W.U..J..............9Y4.+0........yI":..@.o+...0..V..E$T.....[J..*....s...n..B.T[....a..}......d...Cx..-....5..L.<`o?.....)H. ..0..m....4.v..~.$.NH...E.,...;..P....<...$.:....B;1....2..J......h..-.B."\[.(.'..R. ...Vo...z......g..>..8..4.@.?>..v..h.*.....(.g....B.4.=..I..:$v.C.......L...A,..Hg.x7`.>.....dd....F.....x........Ys2.......-....ZlX..^...|....v.`....;~{...bx..)...J...x4..Q.l.W?.u._z2.F.x..Q.?BY..PG....R...i......k..!..c..; I.1..}...h........H...W.}.7N..Q..t..bH1#w.POS...C...*$.}sUd|7.H..'....qj...v...F+...&r.+/....h......*M.v....Hn.."....(......`...G.2...x.p.`.5_.6j.xr4rQ.<.f..K.6f......d....w..9...............<.....z..EE.T.....J.YY...gT.^^..o.........6.b O".{G.Y...9........E..0...mb.3h..Z........V.......Q./....../.}...W.t........0T.z..tM..K..<....%....8..\...e....O...t@.....N/!.......x.^...].5m.@....._...k.r..DS.........G.....e....Z+....N:..K.+e...,..........;...{H..g.P.....jn.'..j~....D.....>.JfD..l8.. R.d........cC......X...UY...8G...wrI}..w.h..^X%..UR....................5...[+.L3#~..y..?.sS.+h.w...mB........&..@.{..T..o.....I.l8.....^G.[.^0....EA....*.v.G`.8....1o!......~.A.(w.?X..\.#x..@..I.Z.&wQ..x%C...3.7.z....f.'..3....n....p....^>..6>C...b...........R.9%.b.|...(......<EMAIL>..../.....i..R....u....U.%$....k.T..w..s.......+......7ID.....Q.a.Y........S.{..v.....uh........g.........z...<...~.d..k.. ..c.|..X*...&......).kP...e..OQ...rQ....?.f..y....s...KE.i...WN.;_w..j....Z(..@.}.P.....B.<.c...{:q...[.p.6.I}....~.....lR....7m3....<....U.k...P.......O.....d.......:$.cC...r......k.+..,...<".....oy..Rr..N..Lf....j.J,...g.{..r....\_......f.-..yt@f(t)W8.........i.EM.....2.6..6.e..8..j.....6.......f..Q?A....y.w'._..2..+..4....X..x..q...B0.s...JsN(.....WMK...p..x.P..EM..l....K.....A..S..SU...OQv.|.L...C......_Q.......j..7.+.Z....q8G.L~......m.........hls..M..6Nb...TJ%.19C.....:2..u...y.-......@yP*.F..z..FC~.ON...J..`?L[...Pv.s..\..x.....{k`....4A.A....!...AeCm...+.....p.].`.2k?..........:....P.E>&.../sR...j.[...fv..&eS..ta...a/A......C....N........O..WxGD.....a...<....o2...3.~.Y.e,,....P)...[......w....%ECm../$.....}.yMS.....j....%..e.......F1\....\......~.B'...;5...e..X...#`..b./...L..O.....y;../.K....... ..Lu.....W.........n...d.`.x.....i..H......;.A....5[........h.v..L..|x.#3..b?..?..*.m.....lt...#w.../.b@U0}F..1....t..%..&...`.]q.S.Q......P.......-.8............k...............8Y...<.....{.......%B.[Y......Ek......;.V..W.i.i.......^k..<%.|..c.....~nTa6.....9..-.....?..rR....ok*.t..i.....h.....w..~V..(.P.0.w..........s.Q .~....i.G.0...9...<~.v.....G.v....,....a.{.k.....a.............4....V.....,.RL+.8.....kS......-...T..u......u.I..:...`Q.o.b:n.......p-^...<....V.4.p..%T.....HU.\. /^Y...yR...n$n.`..".:c.zVg.....kM&.........A.......9L.;............p.H......5..t....{~....4.5.TN....n..8`M._`B;.....lF.x..r~.$F....w...!.y....[.....h.Vk...d.6.$...i....:S..Ob...'.....=..}..'......Q.$..p.2.D.......u..r.IR.7..S"$/C...\.-....G.@...)P.d.....n..`...Y.0...*.o.^.;S.T....z...B{.......*..._..X/R.ES.H#_Yj$.#"@..>X./`}.@....5......GB%..rB.I.-.g<H9...52..S.:T.ej.vY0.Pr.....#@....6.w%...FC.G).l]+.F....SX.!....G.2_.......v.......6g.6....."..K.Wx.IQ.....1..td>..m.M.y..W...-.*4...U@?..zN.._...b...#.N..|Y......Q.s.....Q?+:..}....G`{:. %d..,).x8.......f.Z..z..?<..)U Nw.QX.6Pg....St.lkb.....G.b.z-.`=b..Am@.q.x8n3..C*I..3;x....j.m.s-...5..Z.....p::..v...zd.y%~C.;....Z..U4..<.RC.ZF3A... ..../...Z#.B`5..4............#^.....hN.*.....R..D4..4.T..3.-..57_..R..>.......G..".....!.#.P.\ed#.oogm..#d.r.....L.s..!".}r.....O.d._..|.G....).*......A...l](........hF../].Q.)N.6......#p.h9f..Y<..T."..T..}.qm.$%o.'\hAGF....K6..h.......]..................h.cE..X.S8..ba...>.D..z.(.P...'...qW.{....r.....L..c........(.....@>....9.....X..i"KM..$=....x-...N.S+0-ya..V.a;v.A.1..|..+.T.?.........&.....G$....>..........H...h.APw...U.`H?...<Q:......g3;.d.Le.=..2{:5..3.Y..j.F...U...+...fQ.*w...VnO..cO.A.c[.-+...........1n.r3.{O.....*..v..7...r....D.Ei.TZ[.l|5.............&GN.F..}'yU).vw!.k:lW.I'./e.=.._...@..f........b.....9..I....~..H...k.=..b...@;\.h(........Q..a.%%?..xi!.^f_._{.j..!5...\r.....f.....Mu....W..........0....6.......:..{{n...<..Is..L...%;...R....JPj..........j.p..............4.........~_..7zL..g+.Y.S.....5..G_M7u......t{~..H..`p..5..<..kv)@.OR.Y..........0K..mE.H.u...Z7.(..R@..i2.^<.o=..U..........}....x_...:..9tN.7.[.9w3....Y~u.......N.....B.....O.%].Le.f..'.M.'G..b;i..U.t|./.l...t9....jtF+.E+D9.Y.....:....j.H..g..vWo..5.\..B.1i$.)U{..}3....Wh...
2025-09-06 11:46:32.169 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:236)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.170 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.170 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43230 --> **************:443
2025-09-06 11:46:32.170 | INFO     | __main__:on_message:243 - ....E....^....0.>x.G..d.m...59.g...U>..U.....WY..%(Qt&.Y6.....mks.I/.....`
2025-09-06 11:46:32.170 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at com.android.okhttp.internal.io.RealConnection.connectTls(RealConnection.java:196)
	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:153)
	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.connect(HttpURLConnectionImpl.java:131)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getOutputStream(HttpURLConnectionImpl.java:262)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getOutputStream(DelegatingHttpsURLConnection.java:219)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getOutputStream(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:47)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.171 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.171 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:32.172 | INFO     | __main__:on_message:243 - POST /dict/fwproxy/receive?sethost=gateway.bingviz.microsoftapp.net&isHttps=true&app=edge HTTP/1.1..Accept: application/json..Content-Type: application/json..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: cn.bing.com..Connection: Keep-Alive..Accept-Encoding: gzip..Content-Length: 1206....{"dvid":"309091B7792E4C3E98ECC394387E41C4","events":[{"id":504,"sid":"5F60F710A42645ACB9BBF6EE0E345222","name":"FirstIDADAction","type":"Diagnostic","ts":1757130392412,"data":{"IDAD":"true","IDAD_actions":"click_omni_ntp","adjustId":"E22AC3DE46FF02ADE666AFE5C3E122F4","anaheimId":"-2588324515577707453"}}],"meta":{"appPackage":"com.microsoft.emmx","clientVersion":"139.0.3405.111","build":"Google_Production","market":"zh-CN","applicationId":"5B0A374023184095B4283E7F1ECC8DFB","installId":"-2588324515577707453","os":"Android","osVersion":"34","timezone":"480","deviceModel":"Xiaomi MI 6","locale":"zh-SG","deviceType":"Phone","installSource":"google-play","launchSource":"LAUNCHER-NewTabPage","extSchema":{"user":{"anaheimId":"-2588324515577707453","adjustId":"E22AC3DE46FF02ADE666AFE5C3E122F4","userSignedIn":true,"aadState":0,"signinAccount":"MSA"},"app":{"dataConsent":"On","installNetwork":"Organic","installCampaign":"","installAdgroup":"","installCreative":"","isDefaultBrowser":"false","detectedMarket":"zh-CN"},"device":{"tags":"hasCP=false;isMDM=0;isMAM=0;isSDM=0;isAWP=0;isCMVPN=0;isMAMT=0;isMSAAllowed=1;isNTPC=0;"},"ext.req.requestId":181},"first-install":*************},"schemaVersion":"3.0"}
2025-09-06 11:46:32.172 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:32.234 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.234 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43230 --> **************:443
2025-09-06 11:46:32.235 | INFO     | __main__:on_message:243 - ....../C.Z..M.....i3*...YO.....Ue.]&..o......o.Q...]..t2.2.S;)....(.....r.cI/......uU.51).......<.$..H........e....g..Z.....,QL...t.4[...W...[~....h.G.#>......q.:x.......3...6.).I..v.CQ...]|..WV...~(..(......r...t...HL.....B..3.7..3..$_..T.5d..../.6.\E..F..rE...k.:g......$....`.......:.g1,4..buO'..W..W.zDhL.E....zu".v..|.g.(..i.O5...Ze...N.R.....k.vY...2....-.....=N.?.....C.j.{.......OK.L..TdC..l*.w....PW...K....y.F.|..}$.M...`.....tr.../C.3T[....U...v.....Z.T..z.>0I.a.x8.|zH.,..]..e..Wj-.k.&..D`s9........rY..v.....4...a............q......5.....@+..e..O.n.......2.....d....n.......G..9x...D.ZT...&..(a.*......v...\.4..02[....e..^y.2..Et.?..g.M..g.I...........o.B.....Q.H.u.-9Q.....9..A.7...U+...R.gnI.-.<.#...^I.v.:K_..qe............g....}.Y@.....S..-.{.lt.h'....C<.#48....Q@..n.D..=...zlQT.8U.E.@.1.-.ex8U.D...]_ \w.cz.....Q$..W.eOjc;...z..cG....9X.Xp.......3.....*q.b..............\2##..@...WBx._...w.[..I.F1.ya/pm..tc.!..9..kR.)$Y.a....1..-.SE.@...3.< ........bi.v.'s...+.........!.....[.....e.+.X....{F.x}G......So...~...E.u....S...|.q.........^\.......%n...>.f...!.xS..#S...Pp..........<..."..Q.>......Q&..lz .C.).e..EU.N.|Jv..,q...../VaN./..k...........i...:^N7._v.1e.@...<&#9..B..0.;L......kE........'..,%.%..M<.b..X..{......4.=.f..*.l.....,}"..].......Hni$#...z?o...\E.s.......;E]U9.;..v.......,.a...7`x...*...-(!.......Ty.Bf.........r.L,)L.0.....>N..'...@..-#..T7..@.l.....(!-7...(...`f....FS.t*....U]..>.... .$Jv.s.y..y.u....|G.u..f|..z...C.~..v.9..>T3.`..C.]6..4.>b=3...2n.p......X.;d.%..r...9U'.a..15...U.9....3.o..X.b..)Y....p.-.dy...
2025-09-06 11:46:32.235 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.254 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.254 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43230
2025-09-06 11:46:32.254 | INFO     | __main__:on_message:243 - .....T.1H..I.../\....aA...4..R..[...4.........q.....u."......K.M. ....B.R...;.te...l..@....$[../..m[\....nE.e...S.D..6.@..]d..u.....t!.B.62..).f.Q.8.g.g..DrCGO.]....7.... m.._iv.),(.@Y.8<....i.IB......8.-;.d*i9..#k..D..a.....)...g...m...s.Y..'.(...Q..3s.j..L%].......2.Q...Ed..*.........8|i....J....J6......DF...]L-.....8AjD>..U.......`..i....tW..Y!.........A....q.....z...l....-.T.Z...c...m...........E.......oo..|i...;.>...O....Px..o.c6...........k..w:At..N
2025-09-06 11:46:32.254 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.356 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.356 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43230
2025-09-06 11:46:32.356 | INFO     | __main__:on_message:243 - ......nC#....}...d.xNP.b.X.l..U./..T:...p.?.#...g...Y.j.4y.7..cP...#......'...........v.. ..."oE.(.O[}8]!..Q...~.m.!..2.)T;...r.L).z.j...Yd...}..\.Q.Q.,....c...a.......&.e..^^LL./!......*.3.......OD..~........#....../.....>}.`~.@.p^..<.H..W.<..../.....jh.W......-.k..W.Gv.*.-V..=.`k\.)...*...W8.,.H62...AB.Y....`..p...;!u.'...<.zw..$R...w..r..R.T...`...![../.U..P.....Ol......:...k.J.<..(....Y./T..L....jl...\....g..*g...xp..z.?(.v..2..)....%.......3..\..-...H......7.~..r7d....7...0j..;...Y.R.... .:.:,._p..=<K|\-.4.(G9.:.ms..^..".....o....I...Kwa.Y..Dr....h?"..K.h;C/B..).t..rS.!y..v....Z.dN]V...B).JGv,a8[.3..J..4|h.#HW.'....Yv.....s%'D..Y....%{.......x....6.I.=..U..*Z...N.:....j.&.5......8......<u..u2s>U.y....r].=.._..RA!x|<EMAIL>^R....%5..K....... u.U1....S..A...`#.-x\.2\... c.....>'F?.....gr.Ur..M.ts..... ..1.....u..;e..<z'.+;...R.d=.M(.$.)qI...+5*c.B..b.....H.C.Y.......%..Iln~..^...u.U..#"_...n?m.......,.........`..Q]....@kIJ.r>...^5I...e{..C...LdgV...j..Z.......8.._....X..d.?.PvV1..<...dRL1.T._#..6.u6N0....~S.7. .]...VR.t..Ty.i..S............E.$.2+..1^..8z....yFY........e1]..}..Z.u..."i....6.$-.1..G.......w.....lw.Q.c....G[.p......kw.A......s.......6?p.
2025-09-06 11:46:32.356 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at P20.run(340511105:63)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:32.356 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:32.356 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:32.357 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Cache-Control: private..Content-Length: 15..Content-Type: application/json..Accept-Ranges: bytes..Vary: origin,access-control-request-method,access-control-request-headers..X-AspNetMvc-Version: 5.2..X-UA-Compatible: IE=edge..x-azure-ref: 20250906T034632Z-17f8ccf5d94p2bf6hC1HK1gmxw00000006000000000028qh..X-Cache: CONFIG_NOCACHE..X-AspNet-Version: 4.0.30319..X-Powered-By: ASP.NET..Accept-CH: Sec-CH-UA-Arch, Sec-CH-UA-Bitness, Sec-CH-UA-Full-Version, Sec-CH-UA-Full-Version-List, Sec-CH-UA-Mobile, Sec-CH-UA-Model, Sec-CH-UA-Platform, Sec-CH-UA-Platform-Version..X-MSEdge-Ref: Ref A: 3D91A31C48AB4A2CA69042ACEC2C9C35 Ref B: BJ1EDGE1011 Ref C: 2025-09-06T03:46:32Z..Set-Cookie: _EDGE_S=F=1&SID=33491B469F246D6623710D199EF66C62; path=/; httponly; domain=bing.com..Set-Cookie: _EDGE_V=1; path=/; httponly; expires=Thu, 01-Oct-2026 03:46:32 GMT; domain=bing.com..Set-Cookie: MUID=********************************; samesite=none; path=/; secure; expires=Thu, 01-Oct-2026 03:46:32 GMT; domain=bing.com..Set-Cookie: MUIDB=********************************; path=/; httponly; expires=Thu, 01-Oct-2026 03:46:32 GMT..Date: Sat, 06 Sep 2025 03:46:32 GMT....{"status":"ok"}
2025-09-06 11:46:32.357 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:34.009 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:34.011 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:34.012 | INFO     | __main__:on_message:243 - POST /OneCollector/1.0/ HTTP/1.1..APIKey: be6e4c19699f4fdf9f8c0ec9f9b398ef-25d58bb9-9926-4df7-8e36-bc7afb4b2c34-7563..Client-Id: NO_AUTH..Content-Encoding: deflate..Content-Type: application/bond-compact-binary..Expect: 100-continue..SDK-Version: EVT-Android-C++-No-*********..Upload-Time: 1757130394655..Content-Length: 781..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: mobile.events.data.microsoft.com..Connection: Keep-Alive..Accept-Encoding: gzip....m..n.F..W.`K..6....j...c.~.h.....:.bA...7a.;ko-r...Iz..."o.c...z5x....g(z,Z........~3...<.El'...9..HF.T.._.........}...O!...(."A......,.Q.G!.?6~j......^R....Q<.P..j.......}.G...K..(....Q........P...:.\k.[...F..EA.......7.g*.3..*.06d....m..l...3.]....~<..i^....sc..?.Zq.e....\...B.z`..=.|;.k.x........v}.v..............8.).C.8.<.<B_m......G..`!...`.....'.h..>.........;...Z-t....J...........(.\...f .cU..2S\h......J...;F.O. 7M....`..wm.{Y.^i1G=...r3.[....H..TK%o..@w9@.B.....~;S27.>...h.dT...~2..'.....p.._..JkX..)...W.%..:2.Z..(..E...ko1........5.t..D.!e{......m.CMU.n..N'..x...DU...l..{p.6....q9.*+..G.?.$.c.......\O.@..3.3H..A.W.q:...|..........Pw%...[O|L.K..\...?...8.{4d.D{!N..`.}.;....PW.Sx.|..G.Y.i....Oi..Y9..Bi..4.NU.......iVPy........W...k.OQ...*..Vs.IF.R..}..I.^}n[h.._..
2025-09-06 11:46:34.013 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:34.070 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:34.071 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40415 --> 13.89.179.10:443
2025-09-06 11:46:34.071 | INFO     | __main__:on_message:243 - ........^...sE........i..#.wzN...+.u..r.K..u...yPq.*.F...X3...Ss."...L.nf../......9...[....LD7J.W.X.$.....z.....2........,.._.V+...|.X......W.xc.3.Ly.PN..|.\5.B.#..&..........Ty?v1....5.3.....F./.cO..N..i.y.7[.D.........m.7.......c.. x."./........N....@.P....s.z..h...Q...n..y@..Q..z...d.%.un*...(....7h.?.>.T'...Si...eL.'...-...k7..~CI+,(...w6R.G.s..S..u..PA.....05I..K...Z.7g.v...(.....@o..............y.lr....e..g.gA.........p....K.Ej.(..f.A..y..L/.\x<..<...S...x.0...x....T.y...1...b..Vo.O....*..P:.<....0.....l.x^X.~...H.ui..?a.T......:...'.....7JJ..ZR.L.........k}.....(..<..n.V.........T..D;2-..7....\.H.s....=..|(.ox`.......p,.~_.)iWG......m..."iS...t.-.......4.z5...........xv'..AR.w.Q.g.m..!Fx...a....5"..Y.<bU.s.njQ..%.Q..^...=...5..U.T+c.=x..^.Y...I(..z;.G..8....$.".6.+V..Z...c_.&.!xz_..ov......##@p....4jO....N.l..................j...C.S..2&M.I....&GI..?:....4=:t....n.....O../[.-.....3.4..........n...h^...1.1.O..!1....sL.l.O....!.K..6n....R.sz$.1*\....%....u..j..K.:Pu..`....B7F..g@.ftQ'*.S..2.....g^!..+.)..C...[<......).oaE.....m5*-.#..k.....z].X..?`ss.0Z.RI.t....H|..;.R.g.N.<..T.7v.%eX.X..NAe.65....T.....O.Z.+.}uhS..P.j..dq..jM..3..54.2f...9...9.2........4i..w...>............{.IuVkxj.....T.fl]....QL.Z...}....o..)M.yo}.'1..!..d.........f.|.J%bVU........R...{...f..U36.U.L..
2025-09-06 11:46:34.072 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:34.974 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:34.974 | INFO     | __main__:on_message:235 - [HTTP_recv] 13.89.179.10:443 --> *********:40415
2025-09-06 11:46:34.975 | INFO     | __main__:on_message:243 - .....5w..a.Hs......7./n;r..V...../.....z...V$(...A.F..,!...1Y.,UND........k.@k.v.).....?..gx..`O.Y..... ..@2^...h#F5.y.l."[:.....;.{.........Z.#....AT...'.....9............]...5%......|. ^.q..T......Q.-.....V...n..`ul........\....\..).Ao..p-...w...B......*8.T...*...F.......'..?..YYc.....k.h.......z......Z...p.5..J..h...JQ.u....._....l.1.G...M~4*...  #.....[...!D..w....@g.1>.i.....C.Z).....?....T.u.)J.8nW.*...........0...z.....(6..b\..K>K.7..
2025-09-06 11:46:34.975 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:34.976 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:34.976 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:34.976 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Content-Length: 9..Content-Type: application/json..Server: Microsoft-HTTPAPI/2.0..Strict-Transport-Security: max-age=31536000..time-delta-millis: 845..Access-Control-Allow-Headers: time-delta-millis..Access-Control-Allow-Methods: POST..Access-Control-Allow-Credentials: true..Access-Control-Allow-Origin: *..Access-Control-Expose-Headers: time-delta-millis..Date: Sat, 06 Sep 2025 03:46:35 GMT....{"acc":1}
2025-09-06 11:46:34.976 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:38.944 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:38.945 | INFO     | __main__:on_message:235 - [HTTP_send] *********:48994 --> 204.79.197.237:443
2025-09-06 11:46:38.946 | INFO     | __main__:on_message:243 - ............p.Q.!...dX...7r...\k.._..K..... .....i./a... ...qShg.,:.!^.?.............+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... .3..............?~.k.@dQ7......R.-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:46:38.947 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:39.111 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:39.111 | INFO     | __main__:on_message:235 - [HTTP_send] *********:37556 --> 13.107.21.237:443
2025-09-06 11:46:39.112 | INFO     | __main__:on_message:243 - ................I0....L.......>j......>B... J.,.0=F>8!v...>a#.^?...^.....Hu..........+./.,.0............./.5.............copilot.microsoft.com............................#.........http/1.1..................................3.&.$... ...p3<^k.].........P.A..]p..)....-.....+......................................................................................................................................................................................................................................................
2025-09-06 11:46:39.112 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.-$$Nest$mwriteInternal(Unknown Source:0)
	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:244)
	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:218)
	at okhttp3.internal.connection.a.i(340511105:30)
	at okhttp3.internal.connection.a.e(340511105:151)
	at YZ2.a(340511105:127)
	at S41.intercept(340511105:21)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ZI1.intercept(340511105:23)
	at YD6.b(340511105:121)
	at Dg1.intercept(340511105:64)
	at YD6.b(340511105:121)
	at vF3.intercept(340511105:15)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at FD6.run(340511105:38)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:41.202 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:41.202 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:41.203 | INFO     | __main__:on_message:243 - ..8.........c....b.a'7r.4W..4.`..P..S.H..D|Vi .^.&,5|S.0{.m......
2025-09-06 11:46:41.203 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:41.219 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:41.220 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43146 --> **************:443
2025-09-06 11:46:41.222 | INFO     | __main__:on_message:243 - ....R^<.b....C...b.........U.....,...J.......i;A..e,.E`.0........@..:@..:....`.......z.
2025-09-06 11:46:41.222 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at OE3.flush(340511105:8)
	at okhttp3.internal.http2.c.a(340511105:261)
	at Zk0.intercept(340511105:33)
	at YD6.b(340511105:121)
	at S41.intercept(340511105:137)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at ID6.c(340511105:42)
	at yn5.run(340511105:60)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:41.309 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:41.310 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:41.311 | INFO     | __main__:on_message:243 - ................w...%..|B..\...._u...o`i./....k.I.H.T..j.\...<EMAIL>/...Iw.%.I./w...%.....}....8\..!......_}./8....Y.* .=.J.+j"V....f.N\h*b...J...c.LiMz...w....X"......%....Z..L....n...[..........I.H.T..j.\...J...c.LiMz...w....X"..~.....@.f.e.z~./..aQ....P.[Q...'..7.r.AS...jV4...jcJk.U..w....;.C..:.....I.H.T..j.\...B.....h=_J.......:.........o.R..x.w....>./...|b....<.o_o.W.u. ..n.y....6..{.Jj$.@..$=#5P/...r......XTA..}(...Z.....f.N\h*b...J...cw....>....U...6............L..O...Y.* .<EMAIL>.1w....<z.p....oc...P{.lc..jHzFj.^3U2.=?j..f...A..P.-... ........T......3..w..v.7d..a..W.i.^..m.aw.wAu.08.7...!....x.T.....X.<.....q...F..'Zu..0...y..p$......A,5iY..I...qO...b%i=T:LO..........]..J...E+[R.)V/q....U...m..).0....|...G....S...O_.........A....h*..e?..D.........YT...........ZlX!}._m..m.7....K*.n)w(p_..!...YT.qA.M.................4.(...Z.....f.N\h*b......................V..MLO-V...V*.I,*II,IU.R22025.40U.QJ+...&ehf`.........LIG..(G.JI.$.>3...#H/.4-....477.(>.C............ .........@/. ].(..'.%3.4.M. 3....5%=.1.bGRb1..D......_PY...Q.d..l....=/.4..1....g...l..t....._N_..u...k...y...i......n..d.........|_...{.4..T.J-J..L./.N,.wO-).T....&.}9.y.JVJ.%%..V.....zI.y.z.......E.......f.......................F....N...nji.E.......%j..%.U.....(.,......3./.y>......KV*.(..fV.b.n.Sf^.vF~njAbz.6HZ--3.$...6<..8;..J...# .4.*......P..H..?...# 0.3JIG..@....4UG).8C.J....,...(..(...05..21.. %..... .DIG).([..PG.$..L'.....Pr........d..+YU+..'.d..."n...sV<.Z.b.^===%.........b%+.';...hx.c.....6.+.(..V.....n4....p.=[....}.;....}.......{.>.....9O.7>...qC....H9......]../...}.....n}9s.D...=/..Bh..............g...
2025-09-06 11:46:41.311 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:41.312 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:41.312 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:41.312 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:46:41.312 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:42.775 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:42.776 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:42.778 | INFO     | __main__:on_message:243 - POST /OneCollector/1.0/ HTTP/1.1..APIKey: be6e4c19699f4fdf9f8c0ec9f9b398ef-25d58bb9-9926-4df7-8e36-bc7afb4b2c34-7563..Client-Id: NO_AUTH..Content-Encoding: deflate..Content-Type: application/bond-compact-binary..Expect: 100-continue..SDK-Version: EVT-Android-C++-No-*********..Upload-Time: 1757130403430..Content-Length: 782..User-Agent: Dalvik/2.1.0 (Linux; U; Android 14; MI 6 Build/AP2A.240905.003)..Host: mobile.events.data.microsoft.com..Connection: Keep-Alive..Accept-Encoding: gzip....m..n.F....`K...;...j...c.~.h.....:.bA...7a.;ko,r...iz,z...[...C..k...(....-.....i.?...wv.. .....9..HF.T.............._...B ...8.Dp.....b..A...s..V}....w..L....~...........{l.@.P...R...[..k%.3=V9....:.\k.[...FB..a....m....1.9.dZ.J........^.;...w.x.w.i.p..........E.......<....l}+.Du...=..3.k.x...'.........m..(.1.>.....8.(...8.}.|B_.:...P7.).."<...n.].._.. N..Q.y3..7.s]T?.-t....J...k....qoX.I..bB3...*.R.....x...P.r...........y...t.....,A.....eLU...-.uiQ...TK%o..@w9@.B.'...~;U27.>...h.dX....l>.........=......\mS.e+..K..Ud....P2-...O....g.4?..7....K..".v}/...k...5U);..`2._$...:.J..f.6..k.9=.PO..VYa..z.afV1............P:.9..?...j n.....~....Z0c...Y..!w.......$..r;.}....>..&.n.S/"..... Q.C.....4_..P[.s.....UZBnVNd.P.h*..S....UU....*.r....}t.2..y...j7.a%9.jnS.hY...[M8....m.......
2025-09-06 11:46:42.778 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:42.849 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:42.849 | INFO     | __main__:on_message:235 - [HTTP_send] *********:40415 --> 13.89.179.10:443
2025-09-06 11:46:42.850 | INFO     | __main__:on_message:243 - .........'H...fB..C'..w.T.8.6........<.9..nNW..U.v.}$.M},=..\RBN.....R.>TMB...<@:.7......ea.......;;...E8........6.p."}..r...*x.[h.fti..6/M<I1..O..x..-.,V/....7..........g.F..9^...r}D...x...No`.._.........]..:x...#.A..Q..1.Z.......(..[...P....:"..6...N..*$h..a"....4).N......7..L..l..[)b+o..b..*..=......._.e...u..&|0.0......c...(tn.RE:...4......@8..c5...[F....:-d+\.`....}..I...\0H.X{.6......e............Y...5k.$.~.J.....swZd.>..\/..nM......Y.$..N.(.$.]<,.. ..@..2*;...@..5......w........b.@^..@..dj._.....[vn<..M.a.>....I[...(.}.#.Lr.\.c...t.4.....Xh.Y.X......W.~...A....c...........:.V.......v....C.}\.....O....'.L..p0..ZqJ<.&D.JB\.8#X..^...lwM.uf...Y..)?Pud...q;..4E....C...H.f...O.fi/UZ.a.h.".Dk.g.....:P.<.T..}k).7.k......6..9..W.=g.....&bR...J).Q.T....]... .k(.........+Ff....T.....o.....%....r.?..Z*................{|k.`hf._.G....8....iz..i.4.n..O.).&|&K<...c(..Q]mvV.[$..E..),<EMAIL>...>.+@_O.S.mK#5D.9H.LZ...v..m.`#y...\j.....j...fq.......,i...w......K..Z.|r!7&.wB.A?Mra.,...(....n.T{.BO>."..."......6..?....b.A}...@..*.g^6....&.T?..].LOb..%...<}..Z...a....:jq..4.bC}.khb..T....J.....%.......x..t5.0....._F..%..m..>c...t.....b....F..@.BB3T...4+...H);v..s....[].g.......f...|..x...A8.e...`.2.....a.....H.........^.Ox..D.6.4.A..........:..R'./A..~......ij#.
2025-09-06 11:46:42.850 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at com.android.okhttp.okio.Okio$1.write(Okio.java:78)
	at com.android.okhttp.okio.AsyncTimeout$1.write(AsyncTimeout.java:157)
	at com.android.okhttp.okio.RealBufferedSink.flush(RealBufferedSink.java:222)
	at com.android.okhttp.internal.http.Http1xStream.finishRequest(Http1xStream.java:163)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:748)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:42.989 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:42.990 | INFO     | __main__:on_message:235 - [HTTP_recv] 13.89.179.10:443 --> *********:40415
2025-09-06 11:46:42.991 | INFO     | __main__:on_message:243 - ......g.vw,.?....c.V.....t.c..1..X.F.*{ ..R....u.d.T.~.GJ...U.....A..'.NM....u.......;..2T..Q.t.9...........G..J.9....3Q.0..p..K.6...X.|.R...'...3`...}.;..Mm.?..`|..5...J.Ci....=...._2zo..T....s..L..8.7.....:)...>...D.R.m[....N..+.......@..6^!...`^.#...,.g..Hs 6.....:~..3..D......,.......=...P.a\yuAY..?....TL..0.?A........xp.T.G.P&.`G.F..2...v....s.:eB....7.n.f.._....GX!.i....~.,yp.V.H),........c.W.~...&....]ap@?.ja..00..u..}H.t."(V....v...s
2025-09-06 11:46:42.992 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
	at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
	at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
	at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
	at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
	at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
	at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
	at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
	at com.microsoft.applications.events.Request.run(340511105:28)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:42.993 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:42.994 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:42.994 | INFO     | __main__:on_message:243 - HTTP/1.1 200 OK..Content-Length: 9..Content-Type: application/json..Server: Microsoft-HTTPAPI/2.0..Strict-Transport-Security: max-age=31536000..time-delta-millis: 101..Access-Control-Allow-Headers: time-delta-millis..Access-Control-Allow-Methods: POST..Access-Control-Allow-Credentials: true..Access-Control-Allow-Origin: *..Access-Control-Expose-Headers: time-delta-millis..Date: Sat, 06 Sep 2025 03:46:43 GMT....{"acc":1}
2025-09-06 11:46:42.994 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:54.174 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:54.180 | INFO     | __main__:on_message:235 - [SSL_write] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:54.181 | INFO     | __main__:on_message:243 - ..8.........c....b.a'7r.4W..4.`..P..S.H..D|Vi .^.&,5|S.0{.m......
2025-09-06 11:46:54.182 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:54.200 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:54.202 | INFO     | __main__:on_message:235 - [HTTP_send] *********:43146 --> **************:443
2025-09-06 11:46:54.203 | INFO     | __main__:on_message:243 - ....R...)L...s-...Z*...J..[...yN...d.4...........L.A.g..&z.G.n...Tm........}3(5.......5
2025-09-06 11:46:54.203 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
	at WE5.M2(340511105:39)
	at CD.M2(340511105:47)
	at CD6.flush(340511105:17)
	at OE3.flush(340511105:8)
	at okhttp3.internal.http2.c.a(340511105:261)
	at Zk0.intercept(340511105:33)
	at YD6.b(340511105:121)
	at S41.intercept(340511105:137)
	at YD6.b(340511105:121)
	at Ej0.intercept(340511105:926)
	at YD6.b(340511105:121)
	at Jd0.intercept(340511105:230)
	at YD6.b(340511105:121)
	at mT6.intercept(340511105:348)
	at YD6.b(340511105:121)
	at ID6.e(340511105:105)
	at ID6.c(340511105:42)
	at yn5.run(340511105:60)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:54.368 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:54.369 | INFO     | __main__:on_message:235 - [HTTP_recv] **************:443 --> *********:43146
2025-09-06 11:46:54.370 | INFO     | __main__:on_message:243 - .....^U.v.S"..v..#..Ed..t.O@._....0K......P..}...:Z.sKC.c. . .45?..aH.fB..+.)0.......q...BL.0.v....9.....5x.....$..I1..LVw;.C.;..(4...,y.Z2l..I.AX.j...C.R,....s#..4gfaj.....4.F.\.6.bB......#.:.d=>N.9F.8..q.................!3O.."xP....$*...x9.[[...ML6f.....:"O.... .h..s...~<....X.o....+9..i"..F......b...>.>...>D.g_[.-+`....i.+.`....S.G...7.A6T.X.....l....h;+..V..N=68..R0~..(.t..w..Iv.n.$...Z....3...7.?r)..&R.-...@.?.y.X^.`..M..9...Q....2z...N8...|..<..g./.............?.s /8!"&{Kc.h8BOr]...../]..... oh6..c....<u....rgD......6.I...W.g.K., ..`....y.......95A1=...N.E/,.*..m.;._2b.....?9......+.^.........Q1.#9..&B.Zr.4...[T......a.U.z.m...9ZH..j..k....c.5..g.Lf......L.:_c....=....P...ov..?....g.A.@R..S...........p.%x.....q/....H*...z..F(...&...d..5F.]..@...eZ[....[..U...U..3.]<...,g..O....t.6E..V........h................^n.g.~...(O..<......R.G.R.e.$...<..PxM.)....-?.y[.c...:..K4-'*..........-.)....3...K...!.d.......T.&)......zS#%yV....W.).....X..[..{.DF&k.d>.T..J..QLO.7.@.....#....S.l...^.....fz....c.+R.'Ti...Uz.C...\.."......3....kE.b.%DD..4......-Y.......=2\k.y.<.<)/..C5Tx.......P.o....x......0....VH..{..(....~.7.....7.{.S6..I...C<.D.-..1.....#..#v.%..B@%...l/P.-..)...A.....ab.."4.(.tE.?c...f.K.......W.3R.....(.Q$-*.D~[9..;)v}..?:..._..z.J.%.o:..n..!.kU..tEubZ...=(......B..I...n..".V ......YJ.`y...T.v'dS...&...HG.p..\..!.D.p.....h.&e-S.F.:7..........r.....\W...=~.........vj....,..j?.!.g.x..Mj/<..m...%..oU^.!.G..*.............Nd..YFeA..^a>....8...c!.j..k..s/.....j.K..M=J0..M..7...0.....W..).HO...3.r.....lp....0 ....T.p^....E=P.$.y;Xy[..8.7.. E.....1#m....0.T"..Z. ..^2.D.....n....Q..-z....l./.E.;...5a..{iT-..3S...p?1.......J/..D/..C..M...zT3..r..."&=B..}._......<=.....s.u...........D..<.R.V.....~.^..(3)d....].....o}|R...=.......g.._.@.1..1A._..W0..0l`...?..>..(o@.YPj~/.T6.....I..Ds'...n ........t.l...J].u...............Dqyk..r.......r..<x%.......tg.J$)%.j.............(.'1.!M..p]..8......i?1L......%....!.S..&...>=.++i...2.cu..jky...cF..~.,.....4.M...$!....f..0y....A.........iC.|a..^.....1.......u...:_.|.e..7..`...sx;...;5z.....'..1.)..x)t.e%....m........z}...|....S...rh...W..U.`,._b#..29i......f.Y..1.84r....dI)f....q.....f...D.k...0....:..eU.........a........2....ewG.......hOn.r.m..W.@;OUoG~.q.V.i
2025-09-06 11:46:54.371 | INFO     | __main__:on_message:244 - java.lang.Throwable
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
	at o04.H1(340511105:36)
	at DD.H1(340511105:8)
	at ED6.H0(340511105:23)
	at ED6.v1(340511105:1)
	at okhttp3.internal.http2.d.a(340511105:10)
	at IE3.invoke(340511105:16)
	at ps8.a(340511105:25)
	at us8.a(340511105:16)
	at ss8.run(340511105:18)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
	at java.lang.Thread.run(Thread.java:1012)

2025-09-06 11:46:54.372 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:54.372 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:54.373 | INFO     | __main__:on_message:243 - ..........X...w.K\.736_..u.b.&=LtA..P....0Z.V{Z....{...-i[.D<..o@.p3p.....N.*p..K..)..*n....^R...R..#S^.F.......T....w...%...........z.Bm.\pO....?._jHzFj.^3U2.=?j..f...|..(....X@.....9q........3....)-..jn.%.d..'..w...%.....:.]...............\..../..aQ....P.[Q...'..7.r.m).F..+.g...1.5..._w....X"......%...8.|B......Z}...|,../.s.I.H.T..j.\...J...c.LiMz...w....X"..~.....@.f.e.z~./..aQ....P.[Q...'..7.r.m).F..+.g...1.5..._w....;.C..:.....I.H.T..j.\...B.....h=_J.......:......iLZ7..X.<..w....>./...|b....\e......y..m...:.B..-..?.SQ'....!....x.T....._5.......@8.....'T.7.r.m).F..+.g..w....>....U...6............L..O...Y.* .z....n-k..u@3q..6...o.R..x.w....<z.p....oc...P{.lc..jHzFj.^3U2.=?j..f...A..P.-... ........Jb...J...cw..v.7d..Z..o.A..r.^.O...o......}.!....x.T.....X.<.....q...FWc.........#......@....s...Y-"Lz.ZOj..b...o...?E.'._.%.3.>.{......98.v./...5j..6..'.xii..........9..N.H.....x..0.._Y{..*.&...O&=......#2zl...Y...=.......Z.ya...h.....6....x.. ....J#.m....J2....I1Q.3dFJx.LTz...h...7.kOM........NYz. .>M.U[}I..2SG.8.......(..#.<EMAIL> ...Vz.O_....9 Ah....A,5iY..I...qO...b%i=T:LO..........]..J...E+[R.)V/q....U...n.....5...b......&.X,}... ..jBDi}.....#.....O.../..@....I....N......7..@...Z..'...R-{..!Z..?...E.c[.+]2j*.....k.....m.......})..Z.5.B..h.n%..=Kg2.....k......../...E.c[.+h<...M.".......4.{).SqH..kp.mt.2..V.[.c.....YT......................8..........]....`.B%6.U/\P@.k.....3q..6......4.(...Z.....f.N\m.1h.....................V..MLO-V...V*.I,*II,IU.R22025.40U.QJ+...&ehf`.........LIG..(G.JI.$.>3...#H/.4-....477.(>.C............ .........@/. ].(..'.%3.4.M. 3....5%=.1.bGRb1..D......_PY...Q.d..l....=/.4..1....g...l..t....._N_..u...k...y...i......n..d.........|_...{.4..T.J-J..L./.N,.wO-).T....&.}9.y.JVJ.%%..V.....zI.y.z.......E.......f.......................F....N...nji.E.......%j..%.U.....(.,......3./.y>......KV*.(..fV.b.n.Sf^.vF~njAbz.6HZ--3.$...6<..8;..J...# .4.*......P..H..?...# 0.3JIG..@....4UG).8C.J....,...(..(...05..21.. %..... .DIG).([..PG.$..L'.....Pr........d..+YU+..'.d..."n...sV<.Z.b.^===%.........b%+.';...hx.c.....6.+.(..V.....n4....p.=[....}.;....}.......{.>.....9O.7>...qC....H9......]../...}.....n}9s.D...=/..Bh..............g...
2025-09-06 11:46:54.375 | INFO     | __main__:on_message:244 - None
2025-09-06 11:46:54.377 | INFO     | __main__:on_message:234 - SSL Session: 
2025-09-06 11:46:54.377 | INFO     | __main__:on_message:235 - [SSL_read] 0.0.0.0:0 --> 0.0.0.0:0
2025-09-06 11:46:54.377 | INFO     | __main__:on_message:243 - .........
2025-09-06 11:46:54.377 | INFO     | __main__:on_message:244 - None
